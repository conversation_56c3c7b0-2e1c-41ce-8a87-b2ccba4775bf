package main

import (
	"fmt"
	"log"
)

// SimpleTokenManager 简化的Token资源管理器
// 移除事件系统和并发控制，保持核心业务逻辑不变
type SimpleTokenManager struct {
	tokens         map[string]*Token
	unbindedTokens map[string]*Token
	storage        SimpleStorage // 可选的存储后端
}

// NewSimpleTokenManager 创建简化的Token管理器
func NewSimpleTokenManager() *SimpleTokenManager {
	return &SimpleTokenManager{
		tokens:         make(map[string]*Token),
		unbindedTokens: make(map[string]*Token),
	}
}

// NewSimpleTokenManagerWithStorage 创建带存储的简化Token管理器
func NewSimpleTokenManagerWithStorage(storage SimpleStorage) *SimpleTokenManager {
	return &SimpleTokenManager{
		tokens:         make(map[string]*Token),
		unbindedTokens: make(map[string]*Token),
		storage:        storage,
	}
}

// GetName 获取管理器名称
func (stm *SimpleTokenManager) GetName() string {
	return "SimpleTokenManager"
}

// AddNewTokenWithCapabilities 创建支持自定义ID和能力的Token
func (stm *SimpleTokenManager) AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token {
	// 应用特殊规则（复用balance.go中的逻辑）
	capabilities = applySpecialRulesToCapabilities(capabilities)

	token := &Token{
		ID:             id,
		Apikey:         apikey,
		ApiSecret:      apisecret,
		ApiToken:       apitoken,
		Limit:          limit,
		Used:           0,
		ConnNum:        0,
		Capability:     capabilities,
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}

	stm.tokens[id] = token
	log.Printf("创建 Token: %s (产品支持类型: %v, 限额: %d)", id, capabilities, limit)

	// 将Token放入未绑定列表，等待绑定
	stm.tryBindToken(id)

	// 如果有存储，保存到存储
	if stm.storage != nil {
		if err := stm.storage.Set(fmt.Sprintf("token:%s", id), token); err != nil {
			log.Printf("保存Token到存储失败: %v", err)
		}
	}

	return token
}

// RemoveToken 删除Token
func (stm *SimpleTokenManager) RemoveToken(tokenID string) bool {
	token, exists := stm.tokens[tokenID]
	if !exists {
		return false
	}

	log.Printf("删除 Token: %s (能力: %v)", tokenID, token.Capability)

	// 从本地存储中删除
	delete(stm.unbindedTokens, tokenID)
	delete(stm.tokens, tokenID)

	// 如果有存储，从存储中删除
	if stm.storage != nil {
		if err := stm.storage.Delete(fmt.Sprintf("token:%s", tokenID)); err != nil {
			log.Printf("从存储删除Token失败: %v", err)
		}
	}

	return true
}

// GetToken 获取Token
func (stm *SimpleTokenManager) GetToken(tokenID string) (*Token, bool) {
	token, exists := stm.tokens[tokenID]
	return token, exists
}

// GetAllTokens 获取所有Token
func (stm *SimpleTokenManager) GetAllTokens() map[string]*Token {
	result := make(map[string]*Token)
	for k, v := range stm.tokens {
		result[k] = v
	}
	return result
}

// GetUnbindedTokens 获取未绑定的Token
func (stm *SimpleTokenManager) GetUnbindedTokens() map[string]*Token {
	result := make(map[string]*Token)
	for k, v := range stm.unbindedTokens {
		result[k] = v
	}
	return result
}

// UpdateTokenUsage 更新Token使用量
func (stm *SimpleTokenManager) UpdateTokenUsage(tokenID string, usedDelta int) error {
	token, exists := stm.tokens[tokenID]
	if !exists {
		return fmt.Errorf("Token不存在: %s", tokenID)
	}

	token.Used += usedDelta
	if token.Used < 0 {
		token.Used = 0
	}

	// 如果有存储，更新存储
	if stm.storage != nil {
		if err := stm.storage.Set(fmt.Sprintf("token:%s", tokenID), token); err != nil {
			log.Printf("更新Token到存储失败: %v", err)
		}
	}

	return nil
}

// UpdateTokenConnNum 更新Token连接数
func (stm *SimpleTokenManager) UpdateTokenConnNum(tokenID string, connDelta int) error {
	token, exists := stm.tokens[tokenID]
	if !exists {
		return fmt.Errorf("Token不存在: %s", tokenID)
	}

	token.ConnNum += connDelta
	if token.ConnNum < 0 {
		token.ConnNum = 0
	}

	log.Printf("Token %s 连接数更新到 %d", tokenID, token.ConnNum)

	// 如果有存储，更新存储
	if stm.storage != nil {
		if err := stm.storage.Set(fmt.Sprintf("token:%s", tokenID), token); err != nil {
			log.Printf("更新Token到存储失败: %v", err)
		}
	}

	return nil
}

// tryBindToken 尝试绑定Token到可用的Receiver（简化版本）
func (stm *SimpleTokenManager) tryBindToken(tokenID string) {
	token := stm.tokens[tokenID]
	
	// 将Token放入未绑定列表，等待其他管理器处理
	stm.unbindedTokens[tokenID] = token
	log.Printf("Token %s 等待绑定 (能力: %v)", tokenID, token.Capability)
}

// MarkTokenBound 标记Token已绑定（从未绑定列表中移除）
func (stm *SimpleTokenManager) MarkTokenBound(tokenID string) {
	delete(stm.unbindedTokens, tokenID)
	log.Printf("Token %s 已绑定，从未绑定列表中移除", tokenID)
}

// MarkTokenUnbound 标记Token未绑定（添加到未绑定列表）
func (stm *SimpleTokenManager) MarkTokenUnbound(tokenID string) {
	if token, exists := stm.tokens[tokenID]; exists {
		stm.unbindedTokens[tokenID] = token
		log.Printf("Token %s 标记为未绑定", tokenID)
	}
}

// TryBindToReceiver 尝试将Token绑定到指定的Receiver
// 这是一个直接调用的方法，替代事件驱动
func (stm *SimpleTokenManager) TryBindToReceiver(tokenID string, receiverCapabilityBits int, enableMixedMode bool) bool {
	token, exists := stm.unbindedTokens[tokenID]
	if !exists {
		return false
	}

	// 检查能力兼容性（复用balance.go中的逻辑）
	if isCapabilityCompatible(token.capabilityBits, receiverCapabilityBits, enableMixedMode) && token.ConnNum < 10 {
		// 绑定成功，从未绑定列表中移除
		stm.MarkTokenBound(tokenID)
		
		// 更新连接数
		stm.UpdateTokenConnNum(tokenID, 1)
		
		log.Printf("Token %s 成功绑定到Receiver (Token能力: %v)", tokenID, token.Capability)
		return true
	}

	return false
}

// GetTokensForBinding 获取可用于绑定的Token列表
// 供ReceiverManager调用
func (stm *SimpleTokenManager) GetTokensForBinding() map[string]*Token {
	return stm.GetUnbindedTokens()
}

// HandleReceiverDeleted 处理Receiver删除（直接调用方法）
func (stm *SimpleTokenManager) HandleReceiverDeleted(tokenID string) {
	if tokenID != "" {
		// 减少Token连接数
		if err := stm.UpdateTokenConnNum(tokenID, -1); err != nil {
			log.Printf("更新Token连接数失败: %v", err)
		}
		
		// 将Token标记为未绑定，等待重新绑定
		stm.MarkTokenUnbound(tokenID)
	}
}

// LoadFromStorage 从存储加载Token数据
func (stm *SimpleTokenManager) LoadFromStorage() error {
	if stm.storage == nil {
		return nil
	}

	// 列出所有token键
	data, err := stm.storage.List("token:")
	if err != nil {
		return fmt.Errorf("从存储加载Token失败: %v", err)
	}

	for key, value := range data {
		if token, ok := value.(*Token); ok {
			stm.tokens[token.ID] = token
			log.Printf("从存储加载Token: %s", token.ID)
		}
	}

	return nil
}
