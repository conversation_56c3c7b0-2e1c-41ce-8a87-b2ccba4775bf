package main

import (
	"context"
	"fmt"
	"log"
	"sync"
)

// ProductData 事件数据结构
type ProductData struct {
	Product    *Product `json:"product"`
	ReceiverID string   `json:"receiver_id,omitempty"`
	TokenID    string   `json:"token_id,omitempty"`
}

// ProductManager Product资源管理器
type ProductManager struct {
	products         map[string]*Product
	productsByType   map[int][]*Product
	unbindedProducts map[string]*Product
	enableMixedMode  bool
	eventBus         EventBus
	receiverManager  *ReceiverManager // 引用ReceiverManager以便获取可用Receiver
	tokenManager     *TokenManager    // 引用TokenManager以便更新使用量
	mu               sync.RWMutex
	ctx              context.Context
	cancel           context.CancelFunc
	running          bool
}

// NewProductManager 创建Product管理器
func NewProductManager(eventBus EventBus, receiverManager *ReceiverManager, tokenManager *TokenManager) *ProductManager {
	return &ProductManager{
		products:         make(map[string]*Product),
		productsByType:   make(map[int][]*Product),
		unbindedProducts: make(map[string]*Product),
		enableMixedMode:  false,
		eventBus:         eventBus,
		receiverManager:  receiverManager,
		tokenManager:     tokenManager,
	}
}

// GetName 获取管理器名称
func (pm *ProductManager) GetName() string {
	return "ProductManager"
}

// Start 启动Product管理器
func (pm *ProductManager) Start(ctx context.Context) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.running {
		return fmt.Errorf("ProductManager已在运行")
	}

	pm.ctx, pm.cancel = context.WithCancel(ctx)
	pm.running = true

	// 订阅相关事件
	if err := pm.eventBus.Subscribe(ReceiverCreated, pm.HandleEvent); err != nil {
		return fmt.Errorf("订阅ReceiverCreated事件失败: %v", err)
	}
	if err := pm.eventBus.Subscribe(ReceiverDeleted, pm.HandleEvent); err != nil {
		return fmt.Errorf("订阅ReceiverDeleted事件失败: %v", err)
	}
	if err := pm.eventBus.Subscribe(ReceiverAvailable, pm.HandleEvent); err != nil {
		return fmt.Errorf("订阅ReceiverAvailable事件失败: %v", err)
	}
	if err := pm.eventBus.Subscribe(MixedModeChanged, pm.HandleEvent); err != nil {
		return fmt.Errorf("订阅MixedModeChanged事件失败: %v", err)
	}

	log.Printf("ProductManager启动成功")
	return nil
}

// Stop 停止Product管理器
func (pm *ProductManager) Stop() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if !pm.running {
		return nil
	}

	pm.running = false
	if pm.cancel != nil {
		pm.cancel()
	}

	log.Printf("ProductManager停止成功")
	return nil
}

// HandleEvent 处理事件
func (pm *ProductManager) HandleEvent(event *Event) error {
	switch event.Type {
	case ReceiverCreated:
		return pm.handleReceiverCreated(event)
	case ReceiverDeleted:
		return pm.handleReceiverDeleted(event)
	case ReceiverAvailable:
		return pm.handleReceiverAvailable(event)
	case MixedModeChanged:
		return pm.handleMixedModeChanged(event)
	default:
		return nil
	}
}

// AddProduct 添加产品
func (pm *ProductManager) AddProduct(code, symbol string, productType int) *Product {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值
	if productType >= 3 && productType <= 5 { // stock_us, stock_hk, stock_a
		symbol = code
		log.Printf("股票类型产品 %s，符号自动设置为代码值: %s", code, symbol)
	}

	product := &Product{
		Code:     code,
		Symbol:   symbol,
		Type:     productType,
		Receiver: nil, // 初始时未分配
	}

	pm.products[code] = product
	pm.productsByType[productType] = append(pm.productsByType[productType], product)

	log.Printf("创建产品: %s (类型:%d, 符号:%s)", code, productType, symbol)

	// 发布Product创建事件
	productData := &ProductData{Product: product}
	event := NewEvent(ProductCreated, productData, pm.GetName())
	if err := pm.eventBus.Publish(event); err != nil {
		log.Printf("发布ProductCreated事件失败: %v", err)
	}

	// 尝试自动绑定到合适的 Receiver
	pm.tryBindProduct(product)

	return product
}

// RemoveProduct 删除产品
func (pm *ProductManager) RemoveProduct(code string) bool {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	product, exists := pm.products[code]
	if !exists {
		return false
	}

	// 如果产品绑定了 Receiver，需要减少使用量
	if product.Receiver != nil {
		// 通过ReceiverManager更新使用量
		if pm.receiverManager != nil {
			if err := pm.receiverManager.UpdateReceiverUsage(product.Receiver.ID, -1); err != nil {
				log.Printf("更新Receiver使用量失败: %v", err)
			}
		}

		// 通过TokenManager更新使用量
		if product.Receiver.TokenID != "" && pm.tokenManager != nil {
			if err := pm.tokenManager.UpdateTokenUsage(product.Receiver.TokenID, -1); err != nil {
				log.Printf("更新Token使用量失败: %v", err)
			}
		}

		log.Printf("删除产品 %s，Receiver %s 使用量减少", code, product.Receiver.ID)
	}

	// 从按类型分组的切片中移除
	products := pm.productsByType[product.Type]
	for i, p := range products {
		if p.Code == code {
			pm.productsByType[product.Type] = append(products[:i], products[i+1:]...)
			break
		}
	}

	delete(pm.products, code)
	delete(pm.unbindedProducts, code)
	log.Printf("删除产品: %s (类型:%d)", code, product.Type)

	// 发布Product删除事件
	productData := &ProductData{Product: product}
	event := NewEvent(ProductDeleted, productData, pm.GetName())
	if err := pm.eventBus.Publish(event); err != nil {
		log.Printf("发布ProductDeleted事件失败: %v", err)
	}

	return true
}

// AddProductsBatch 批量添加产品
func (pm *ProductManager) AddProductsBatch(opt *ProductOpt) []*Product {
	var products []*Product

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		var symbol string

		// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值
		if opt.Type >= 3 && opt.Type <= 5 { // stock_us, stock_hk, stock_a
			symbol = code
		} else {
			symbol = code // 默认也使用 code 作为 symbol，可以后续修改
		}

		product := pm.AddProduct(code, symbol, opt.Type)
		products = append(products, product)

		log.Printf("批量创建产品: %s (类型:%d, 符号:%s)", code, opt.Type, symbol)
	}

	log.Printf("批量创建完成，共创建 %d 个产品", len(products))
	return products
}

// RemoveProductsBatch 批量删除产品
func (pm *ProductManager) RemoveProductsBatch(opt *ProductOpt) int {
	removedCount := 0

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		if pm.RemoveProduct(code) {
			removedCount++
			log.Printf("批量删除产品: %s", code)
		}
	}

	log.Printf("批量删除完成，共删除 %d 个产品", removedCount)
	return removedCount
}

// AllocateProduct 分配产品
func (pm *ProductManager) AllocateProduct(productType int) (receiverID, tokenID string) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	if pm.receiverManager == nil {
		log.Printf("ReceiverManager未初始化，无法分配产品")
		return "", ""
	}

	// 获取可用的Receiver
	availableReceiversByType := pm.receiverManager.GetAvailableReceiversByType()

	// 阶段1：尝试完全匹配的receiver（优先级最高）
	availableReceivers := availableReceiversByType[productType]
	if len(availableReceivers) > 0 {
		receiver := availableReceivers[0]

		// 检查receiver和token是否还有容量，并且token支持该产品类型
		targetCapability := productTypeToCapability(productType)
		if receiver.Used < 500 && receiver.TokenID != "" {
			// 获取Token信息
			if pm.tokenManager != nil {
				if token, exists := pm.tokenManager.GetToken(receiver.TokenID); exists {
					if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
						// 分配产品 - 更新使用量
						if err := pm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
							log.Printf("更新Receiver使用量失败: %v", err)
							return "", ""
						}
						if err := pm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
							log.Printf("更新Token使用量失败: %v", err)
							// 回滚Receiver使用量
							pm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
							return "", ""
						}

						log.Printf("产品分配成功: Receiver %s, Token %s (类型:%d)", receiver.ID, receiver.TokenID, productType)
						return receiver.ID, receiver.TokenID
					}
				}
			}
		}
	}

	// 阶段2：如果启用混用模式，尝试兼容类型的receiver
	if pm.enableMixedMode {
		for _, receivers := range availableReceiversByType {
			if len(receivers) > 0 {
				receiver := receivers[0]

				// 检查token是否支持该产品类型
				targetCapability := productTypeToCapability(productType)
				if receiver.Used < 500 && receiver.TokenID != "" {
					if pm.tokenManager != nil {
						if token, exists := pm.tokenManager.GetToken(receiver.TokenID); exists {
							if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
								// 分配产品 - 更新使用量
								if err := pm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
									log.Printf("更新Receiver使用量失败: %v", err)
									continue
								}
								if err := pm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
									log.Printf("更新Token使用量失败: %v", err)
									// 回滚Receiver使用量
									pm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
									continue
								}

								log.Printf("产品分配成功(混用模式): Receiver %s, Token %s (类型:%d)", receiver.ID, receiver.TokenID, productType)
								return receiver.ID, receiver.TokenID
							}
						}
					}
				}
			}
		}
	}

	// 阶段3：没有可用的receiver，分配失败
	log.Printf("产品分配失败: 没有可用的Receiver (类型:%d)", productType)
	return "", ""
}

// GetProduct 获取产品
func (pm *ProductManager) GetProduct(code string) (*Product, bool) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	product, exists := pm.products[code]
	return product, exists
}

// GetAllProducts 获取所有产品
func (pm *ProductManager) GetAllProducts() map[string]*Product {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]*Product)
	for k, v := range pm.products {
		result[k] = v
	}
	return result
}

// GetProductsByType 获取按类型分组的产品
func (pm *ProductManager) GetProductsByType() map[int][]*Product {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[int][]*Product)
	for k, v := range pm.productsByType {
		result[k] = make([]*Product, len(v))
		copy(result[k], v)
	}
	return result
}

// SetMixedMode 设置混用模式
func (pm *ProductManager) SetMixedMode(enabled bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.enableMixedMode != enabled {
		pm.enableMixedMode = enabled
		log.Printf("ProductManager混用模式设置为: %v", enabled)
	}
}

// tryBindProduct 尝试为产品绑定合适的Receiver
func (pm *ProductManager) tryBindProduct(product *Product) {
	if pm.receiverManager == nil || pm.tokenManager == nil {
		pm.unbindedProducts[product.Code] = product
		log.Printf("产品 %s 暂时未绑定，等待管理器初始化", product.Code)
		return
	}

	targetCapability := productTypeToCapability(product.Type)

	// 查找可用的Receiver
	allReceivers := pm.receiverManager.GetAllReceivers()
	for _, receiver := range allReceivers {
		// 检查Receiver是否有容量且已绑定Token
		if receiver.Used < 500 && receiver.TokenID != "" {
			// 检查Receiver是否支持该能力
			if hasCapability(receiver.capabilityBits, targetCapability) {
				// 检查绑定的Token是否也支持该能力且有容量
				if token, exists := pm.tokenManager.GetToken(receiver.TokenID); exists {
					if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
						// 绑定产品到该Receiver
						product.Receiver = receiver

						// 更新使用量
						if err := pm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
							log.Printf("更新Receiver使用量失败: %v", err)
							product.Receiver = nil
							continue
						}
						if err := pm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
							log.Printf("更新Token使用量失败: %v", err)
							// 回滚Receiver使用量
							pm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
							product.Receiver = nil
							continue
						}

						log.Printf("产品 %s (类型:%d, 能力:%s) 自动绑定到 Receiver %s (Token: %s)",
							product.Code, product.Type, targetCapability, receiver.ID, token.ID)

						// 发布产品绑定事件
						productData := &ProductData{
							Product:    product,
							ReceiverID: receiver.ID,
							TokenID:    receiver.TokenID,
						}
						event := NewEvent(ProductBound, productData, pm.GetName())
						if err := pm.eventBus.Publish(event); err != nil {
							log.Printf("发布ProductBound事件失败: %v", err)
						}

						return
					}
				}
			}
		}
	}

	// 未找到合适的Receiver，放入未绑定列表
	pm.unbindedProducts[product.Code] = product
	log.Printf("产品 %s (类型:%d, 能力:%s) 未找到合适的 Receiver 进行绑定",
		product.Code, product.Type, targetCapability)
}

// tryBindUnboundProducts 尝试为所有未绑定的产品进行绑定
func (pm *ProductManager) tryBindUnboundProducts() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	unboundCount := 0
	boundCount := 0

	// 创建未绑定产品的副本以避免并发修改
	unboundProducts := make([]*Product, 0, len(pm.unbindedProducts))
	for _, product := range pm.unbindedProducts {
		unboundProducts = append(unboundProducts, product)
	}

	for _, product := range unboundProducts {
		if product.Receiver == nil {
			unboundCount++
			oldReceiver := product.Receiver
			pm.tryBindProduct(product)
			if product.Receiver != nil && product.Receiver != oldReceiver {
				boundCount++
				// 从未绑定列表中移除
				delete(pm.unbindedProducts, product.Code)
			}
		}
	}

	if unboundCount > 0 {
		log.Printf("尝试绑定未绑定产品: 总计 %d 个，成功绑定 %d 个", unboundCount, boundCount)
	}
}

// handleReceiverCreated 处理Receiver创建事件
func (pm *ProductManager) handleReceiverCreated(event *Event) error {
	log.Printf("ProductManager收到ReceiverCreated事件，尝试绑定未绑定的产品")
	pm.tryBindUnboundProducts()
	return nil
}

// handleReceiverDeleted 处理Receiver删除事件
func (pm *ProductManager) handleReceiverDeleted(event *Event) error {
	receiverData, ok := event.Data.(*ReceiverData)
	if !ok {
		return fmt.Errorf("ReceiverDeleted事件数据格式错误")
	}

	receiver := receiverData.Receiver
	log.Printf("ProductManager收到ReceiverDeleted事件: %s", receiver.ID)

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 解绑所有相关的产品
	var unboundProducts []string
	for _, product := range pm.products {
		if product.Receiver != nil && product.Receiver.ID == receiver.ID {
			product.Receiver = nil
			pm.unbindedProducts[product.Code] = product
			unboundProducts = append(unboundProducts, product.Code)
		}
	}

	if len(unboundProducts) > 0 {
		log.Printf("Receiver %s 删除后，解绑了 %d 个产品: %v", receiver.ID, len(unboundProducts), unboundProducts)

		// 尝试重新绑定产品
		go func() {
			pm.tryBindUnboundProducts()
		}()
	}

	return nil
}

// handleReceiverAvailable 处理Receiver可用事件
func (pm *ProductManager) handleReceiverAvailable(event *Event) error {
	log.Printf("ProductManager收到ReceiverAvailable事件，尝试绑定未绑定的产品")
	pm.tryBindUnboundProducts()
	return nil
}

// handleMixedModeChanged 处理混用模式变更事件
func (pm *ProductManager) handleMixedModeChanged(event *Event) error {
	data, ok := event.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("MixedModeChanged事件数据格式错误")
	}

	enableMixed, ok := data["enable_mixed_mode"].(bool)
	if !ok {
		return fmt.Errorf("混用模式设置缺失")
	}

	pm.SetMixedMode(enableMixed)
	log.Printf("ProductManager收到混用模式变更事件: %v", enableMixed)

	// 混用模式变更后，尝试重新绑定产品
	pm.tryBindUnboundProducts()

	return nil
}
