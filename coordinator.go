package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// Coordinator 系统协调器，管理各个资源管理器的生命周期，提供统一的对外接口
type Coordinator struct {
	eventBus        EventBus
	tokenManager    *TokenManager
	receiverManager *ReceiverManager
	productManager  *ProductManager
	storage         Storage // 存储接口

	// 保持与Balancer兼容的字段
	enableMixedMode bool

	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
}

// NewCoordinator 创建系统协调器
func NewCoordinator() *Coordinator {
	return NewCoordinatorWithStorage(nil)
}

// NewCoordinatorWithStorage 创建带存储的系统协调器
func NewCoordinatorWithStorage(storage Storage) *Coordinator {
	eventBus := NewMemoryEventBus()

	// 如果没有提供存储，使用默认的内存存储
	if storage == nil {
		storage = NewMemoryStorage()
	}

	coordinator := &Coordinator{
		eventBus:        eventBus,
		storage:         storage,
		enableMixedMode: false,
	}

	// 创建各个管理器
	coordinator.tokenManager = NewTokenManager(eventBus)
	coordinator.receiverManager = NewReceiverManager(eventBus, coordinator.tokenManager)
	coordinator.productManager = NewProductManager(eventBus, coordinator.receiverManager, coordinator.tokenManager)

	return coordinator
}

// Start 启动协调器和所有管理器
func (c *Coordinator) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		return fmt.Errorf("Coordinator已在运行")
	}

	c.ctx, c.cancel = context.WithCancel(ctx)
	c.running = true

	// 连接存储
	if c.storage != nil {
		if memStorage, ok := c.storage.(*MemoryStorage); ok {
			if err := memStorage.Connect(c.ctx); err != nil {
				return fmt.Errorf("连接存储失败: %v", err)
			}
		}
		// 为etcd存储预留连接逻辑
		// if etcdStorage, ok := c.storage.(*EtcdStorage); ok {
		//     if err := etcdStorage.Connect(c.ctx); err != nil {
		//         return fmt.Errorf("连接etcd存储失败: %v", err)
		//     }
		// }
	}

	// 启动事件总线
	if err := c.eventBus.Start(c.ctx); err != nil {
		return fmt.Errorf("启动事件总线失败: %v", err)
	}

	// 启动各个管理器
	if err := c.tokenManager.Start(c.ctx); err != nil {
		return fmt.Errorf("启动TokenManager失败: %v", err)
	}

	if err := c.receiverManager.Start(c.ctx); err != nil {
		return fmt.Errorf("启动ReceiverManager失败: %v", err)
	}

	if err := c.productManager.Start(c.ctx); err != nil {
		return fmt.Errorf("启动ProductManager失败: %v", err)
	}

	// 发布系统启动事件
	event := NewEvent(SystemStarted, nil, "Coordinator")
	if err := c.eventBus.Publish(event); err != nil {
		log.Printf("发布SystemStarted事件失败: %v", err)
	}

	log.Printf("Coordinator启动成功")
	return nil
}

// Stop 停止协调器和所有管理器
func (c *Coordinator) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return nil
	}

	c.running = false

	// 发布系统停止事件
	event := NewEvent(SystemStopped, nil, "Coordinator")
	if err := c.eventBus.Publish(event); err != nil {
		log.Printf("发布SystemStopped事件失败: %v", err)
	}

	// 停止各个管理器
	if err := c.productManager.Stop(); err != nil {
		log.Printf("停止ProductManager失败: %v", err)
	}

	if err := c.receiverManager.Stop(); err != nil {
		log.Printf("停止ReceiverManager失败: %v", err)
	}

	if err := c.tokenManager.Stop(); err != nil {
		log.Printf("停止TokenManager失败: %v", err)
	}

	// 停止事件总线
	if err := c.eventBus.Stop(); err != nil {
		log.Printf("停止事件总线失败: %v", err)
	}

	// 关闭存储连接
	if c.storage != nil {
		if err := c.storage.Close(); err != nil {
			log.Printf("关闭存储连接失败: %v", err)
		}
	}

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	log.Printf("Coordinator停止成功")
	return nil
}

// IsRunning 检查协调器是否运行中
func (c *Coordinator) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

// ===== 与Balancer兼容的API接口 =====

// AddNewTokenWithCapabilities 创建支持自定义ID和能力的Token
func (c *Coordinator) AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法创建Token")
		return nil
	}

	return c.tokenManager.AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken, limit, capabilities)
}

// RemoveToken 删除Token
func (c *Coordinator) RemoveToken(tokenID string) bool {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法删除Token")
		return false
	}

	return c.tokenManager.RemoveToken(tokenID)
}

// AddNewReceiverWithCapabilities 创建支持自定义ID和能力的Receiver
func (c *Coordinator) AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法创建Receiver")
		return nil
	}

	return c.receiverManager.AddNewReceiverWithCapabilities(id, capabilities)
}

// RemoveReceiver 删除Receiver
func (c *Coordinator) RemoveReceiver(receiverID string) bool {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法删除Receiver")
		return false
	}

	return c.receiverManager.RemoveReceiver(receiverID)
}

// AddProduct 添加产品
func (c *Coordinator) AddProduct(code, symbol string, productType int) *Product {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法创建产品")
		return nil
	}

	return c.productManager.AddProduct(code, symbol, productType)
}

// RemoveProduct 删除产品
func (c *Coordinator) RemoveProduct(code string) bool {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法删除产品")
		return false
	}

	return c.productManager.RemoveProduct(code)
}

// AddProductsBatch 批量添加产品
func (c *Coordinator) AddProductsBatch(opt *ProductOpt) []*Product {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法批量创建产品")
		return nil
	}

	return c.productManager.AddProductsBatch(opt)
}

// RemoveProductsBatch 批量删除产品
func (c *Coordinator) RemoveProductsBatch(opt *ProductOpt) int {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法批量删除产品")
		return 0
	}

	return c.productManager.RemoveProductsBatch(opt)
}

// AllocateProduct 分配产品
func (c *Coordinator) AllocateProduct(productType int) (receiverID, tokenID string) {
	if !c.IsRunning() {
		log.Printf("Coordinator未运行，无法分配产品")
		return "", ""
	}

	return c.productManager.AllocateProduct(productType)
}

// SetMixedMode 设置混用模式
func (c *Coordinator) SetMixedMode(enabled bool) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.enableMixedMode != enabled {
		c.enableMixedMode = enabled
		log.Printf("Coordinator混用模式设置为: %v", enabled)

		// 通知各个管理器
		if c.receiverManager != nil {
			c.receiverManager.SetMixedMode(enabled)
		}
		if c.productManager != nil {
			c.productManager.SetMixedMode(enabled)
		}
	}
}

// GetCapabilityInfoFromStrings 从能力字符串数组获取能力信息
func (c *Coordinator) GetCapabilityInfoFromStrings(capabilities []string) (bits int, description string) {
	bits = calculateCapabilityBitsFromStrings(capabilities)
	description = getCapabilityDescription(bits)
	return bits, description
}

// ===== 数据访问接口 =====

// GetTokens 获取所有Token（兼容Balancer.Tokens字段）
func (c *Coordinator) GetTokens() map[string]*Token {
	if c.tokenManager == nil {
		return make(map[string]*Token)
	}
	return c.tokenManager.GetAllTokens()
}

// GetReceivers 获取所有Receiver（兼容Balancer.Receivers字段）
func (c *Coordinator) GetReceivers() map[string]*Receiver {
	if c.receiverManager == nil {
		return make(map[string]*Receiver)
	}
	return c.receiverManager.GetAllReceivers()
}

// GetProducts 获取所有Product（兼容Balancer.Products字段）
func (c *Coordinator) GetProducts() map[string]*Product {
	if c.productManager == nil {
		return make(map[string]*Product)
	}
	return c.productManager.GetAllProducts()
}

// GetProductsByType 获取按类型分组的产品（兼容Balancer.ProductsByType字段）
func (c *Coordinator) GetProductsByType() map[int][]*Product {
	if c.productManager == nil {
		return make(map[int][]*Product)
	}
	return c.productManager.GetProductsByType()
}

// GetAvailableReceiversByType 获取按类型分组的可用Receiver（兼容Balancer.AvailableReceiversByType字段）
func (c *Coordinator) GetAvailableReceiversByType() map[int][]*Receiver {
	if c.receiverManager == nil {
		return make(map[int][]*Receiver)
	}
	return c.receiverManager.GetAvailableReceiversByType()
}

// GetUnbindedTokens 获取未绑定的Token（兼容Balancer.UnbindedTokens字段）
func (c *Coordinator) GetUnbindedTokens() map[string]*Token {
	if c.tokenManager == nil {
		return make(map[string]*Token)
	}
	return c.tokenManager.GetUnbindedTokens()
}

// GetEnableMixedMode 获取混用模式状态（兼容Balancer.EnableMixedMode字段）
func (c *Coordinator) GetEnableMixedMode() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.enableMixedMode
}

// ===== 同步等待机制 =====

// WaitForEventProcessing 等待事件处理完成（用于同步操作）
func (c *Coordinator) WaitForEventProcessing(timeout time.Duration) error {
	if !c.IsRunning() {
		return fmt.Errorf("Coordinator未运行")
	}

	// 简单的等待机制：等待一小段时间让事件处理完成
	// 在实际生产环境中，可以实现更复杂的同步机制
	time.Sleep(10 * time.Millisecond)
	return nil
}

// ===== 健康检查和状态查询 =====

// GetStatus 获取系统状态
func (c *Coordinator) GetStatus() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	status := map[string]interface{}{
		"running":           c.running,
		"enable_mixed_mode": c.enableMixedMode,
		"event_bus_running": c.eventBus.IsRunning(),
	}

	if c.tokenManager != nil {
		tokens := c.tokenManager.GetAllTokens()
		unbindedTokens := c.tokenManager.GetUnbindedTokens()
		status["token_count"] = len(tokens)
		status["unbound_token_count"] = len(unbindedTokens)
	}

	if c.receiverManager != nil {
		receivers := c.receiverManager.GetAllReceivers()
		availableReceivers := c.receiverManager.GetAvailableReceiversByType()
		status["receiver_count"] = len(receivers)

		totalAvailable := 0
		for _, receivers := range availableReceivers {
			totalAvailable += len(receivers)
		}
		status["available_receiver_count"] = totalAvailable
	}

	if c.productManager != nil {
		products := c.productManager.GetAllProducts()
		status["product_count"] = len(products)
	}

	return status
}

// ===== 管理器访问接口（用于高级操作） =====

// GetTokenManager 获取TokenManager（用于高级操作）
func (c *Coordinator) GetTokenManager() *TokenManager {
	return c.tokenManager
}

// GetReceiverManager 获取ReceiverManager（用于高级操作）
func (c *Coordinator) GetReceiverManager() *ReceiverManager {
	return c.receiverManager
}

// GetProductManager 获取ProductManager（用于高级操作）
func (c *Coordinator) GetProductManager() *ProductManager {
	return c.productManager
}

// GetEventBus 获取EventBus（用于高级操作）
func (c *Coordinator) GetEventBus() EventBus {
	return c.eventBus
}

// GetStorage 获取Storage（用于高级操作）
func (c *Coordinator) GetStorage() Storage {
	return c.storage
}

// ===== 兼容性方法（保持与原Balancer完全一致） =====

// Tokens 属性访问器（兼容直接字段访问）
func (c *Coordinator) Tokens() map[string]*Token {
	return c.GetTokens()
}

// Receivers 属性访问器（兼容直接字段访问）
func (c *Coordinator) Receivers() map[string]*Receiver {
	return c.GetReceivers()
}

// Products 属性访问器（兼容直接字段访问）
func (c *Coordinator) Products() map[string]*Product {
	return c.GetProducts()
}

// ProductsByType 属性访问器（兼容直接字段访问）
func (c *Coordinator) ProductsByType() map[int][]*Product {
	return c.GetProductsByType()
}

// AvailableReceiversByType 属性访问器（兼容直接字段访问）
func (c *Coordinator) AvailableReceiversByType() map[int][]*Receiver {
	return c.GetAvailableReceiversByType()
}

// UnbindedTokens 属性访问器（兼容直接字段访问）
func (c *Coordinator) UnbindedTokens() map[string]*Token {
	return c.GetUnbindedTokens()
}

// EnableMixedMode 属性访问器（兼容直接字段访问）
func (c *Coordinator) EnableMixedMode() bool {
	return c.GetEnableMixedMode()
}

// ===== 事件发布接口（用于外部触发事件） =====

// PublishEvent 发布自定义事件
func (c *Coordinator) PublishEvent(eventType EventType, data interface{}, source string) error {
	if !c.IsRunning() {
		return fmt.Errorf("Coordinator未运行")
	}

	event := NewEvent(eventType, data, source)
	return c.eventBus.Publish(event)
}

// ===== 批量操作和高级功能 =====

// GetSystemMetrics 获取系统指标
func (c *Coordinator) GetSystemMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})

	// Token指标
	if c.tokenManager != nil {
		tokens := c.tokenManager.GetAllTokens()
		totalTokenUsage := 0
		totalTokenLimit := 0
		totalConnections := 0

		for _, token := range tokens {
			totalTokenUsage += token.Used
			totalTokenLimit += token.Limit
			totalConnections += token.ConnNum
		}

		metrics["token_metrics"] = map[string]interface{}{
			"total_count":       len(tokens),
			"total_usage":       totalTokenUsage,
			"total_limit":       totalTokenLimit,
			"total_connections": totalConnections,
			"usage_rate":        float64(totalTokenUsage) / float64(totalTokenLimit),
		}
	}

	// Receiver指标
	if c.receiverManager != nil {
		receivers := c.receiverManager.GetAllReceivers()
		totalReceiverUsage := 0
		boundReceivers := 0

		for _, receiver := range receivers {
			totalReceiverUsage += receiver.Used
			if receiver.TokenID != "" {
				boundReceivers++
			}
		}

		metrics["receiver_metrics"] = map[string]interface{}{
			"total_count":   len(receivers),
			"bound_count":   boundReceivers,
			"total_usage":   totalReceiverUsage,
			"average_usage": float64(totalReceiverUsage) / float64(len(receivers)),
			"binding_rate":  float64(boundReceivers) / float64(len(receivers)),
		}
	}

	// Product指标
	if c.productManager != nil {
		products := c.productManager.GetAllProducts()
		boundProducts := 0

		for _, product := range products {
			if product.Receiver != nil {
				boundProducts++
			}
		}

		metrics["product_metrics"] = map[string]interface{}{
			"total_count":  len(products),
			"bound_count":  boundProducts,
			"binding_rate": float64(boundProducts) / float64(len(products)),
		}
	}

	return metrics
}

// ===== 存储操作接口 =====

// StoreData 存储数据到存储后端
func (c *Coordinator) StoreData(ctx context.Context, key string, value interface{}) error {
	if c.storage == nil {
		return fmt.Errorf("存储未初始化")
	}
	return c.storage.Set(ctx, key, value)
}

// LoadData 从存储后端加载数据
func (c *Coordinator) LoadData(ctx context.Context, key string) (interface{}, error) {
	if c.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}
	return c.storage.Get(ctx, key)
}

// DeleteData 从存储后端删除数据
func (c *Coordinator) DeleteData(ctx context.Context, key string) error {
	if c.storage == nil {
		return fmt.Errorf("存储未初始化")
	}
	return c.storage.Delete(ctx, key)
}

// ListData 列出存储后端的数据
func (c *Coordinator) ListData(ctx context.Context, prefix string) (map[string]interface{}, error) {
	if c.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}
	return c.storage.List(ctx, prefix)
}

// WatchData 监听存储后端的数据变更
func (c *Coordinator) WatchData(ctx context.Context, prefix string, callback WatchCallback) error {
	if c.storage == nil {
		return fmt.Errorf("存储未初始化")
	}
	return c.storage.Watch(ctx, prefix, callback)
}

// ===== 数据持久化方法（为etcd集成预留） =====

// PersistToken 持久化Token数据
func (c *Coordinator) PersistToken(ctx context.Context, token *Token) error {
	if c.storage == nil {
		return nil // 如果没有存储，跳过持久化
	}

	key := fmt.Sprintf("/tokens/%s", token.ID)
	return c.storage.Set(ctx, key, token)
}

// LoadToken 从存储加载Token数据
func (c *Coordinator) LoadToken(ctx context.Context, tokenID string) (*Token, error) {
	if c.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}

	key := fmt.Sprintf("/tokens/%s", tokenID)
	data, err := c.storage.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	if token, ok := data.(*Token); ok {
		return token, nil
	}

	return nil, fmt.Errorf("数据类型错误")
}

// PersistReceiver 持久化Receiver数据
func (c *Coordinator) PersistReceiver(ctx context.Context, receiver *Receiver) error {
	if c.storage == nil {
		return nil // 如果没有存储，跳过持久化
	}

	key := fmt.Sprintf("/receivers/%s", receiver.ID)
	return c.storage.Set(ctx, key, receiver)
}

// LoadReceiver 从存储加载Receiver数据
func (c *Coordinator) LoadReceiver(ctx context.Context, receiverID string) (*Receiver, error) {
	if c.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}

	key := fmt.Sprintf("/receivers/%s", receiverID)
	data, err := c.storage.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	if receiver, ok := data.(*Receiver); ok {
		return receiver, nil
	}

	return nil, fmt.Errorf("数据类型错误")
}

// PersistProduct 持久化Product数据
func (c *Coordinator) PersistProduct(ctx context.Context, product *Product) error {
	if c.storage == nil {
		return nil // 如果没有存储，跳过持久化
	}

	key := fmt.Sprintf("/products/%s", product.Code)
	return c.storage.Set(ctx, key, product)
}

// LoadProduct 从存储加载Product数据
func (c *Coordinator) LoadProduct(ctx context.Context, productCode string) (*Product, error) {
	if c.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}

	key := fmt.Sprintf("/products/%s", productCode)
	data, err := c.storage.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	if product, ok := data.(*Product); ok {
		return product, nil
	}

	return nil, fmt.Errorf("数据类型错误")
}
