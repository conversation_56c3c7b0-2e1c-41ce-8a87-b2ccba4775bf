package main

import (
	"fmt"
	"log"
)

// SimpleCoordinator 简化的系统协调器
// 移除复杂的生命周期管理和事件系统，保持API兼容性
type SimpleCoordinator struct {
	tokenManager    *SimpleTokenManager
	receiverManager *SimpleReceiverManager
	productManager  *SimpleProductManager
	enableMixedMode bool
	storage         SimpleStorage // 可选的存储后端
}

// NewSimpleCoordinator 创建简化的协调器
func NewSimpleCoordinator() *SimpleCoordinator {
	// 创建各个管理器
	tokenManager := NewSimpleTokenManager()
	receiverManager := NewSimpleReceiverManager(tokenManager)
	productManager := NewSimpleProductManager(receiverManager, tokenManager)

	return &SimpleCoordinator{
		tokenManager:    tokenManager,
		receiverManager: receiverManager,
		productManager:  productManager,
		enableMixedMode: false,
	}
}

// NewSimpleCoordinatorWithStorage 创建带存储的简化协调器
func NewSimpleCoordinatorWithStorage(storage SimpleStorage) *SimpleCoordinator {
	// 创建各个管理器
	tokenManager := NewSimpleTokenManagerWithStorage(storage)
	receiverManager := NewSimpleReceiverManagerWithStorage(tokenManager, storage)
	productManager := NewSimpleProductManagerWithStorage(receiverManager, tokenManager, storage)

	return &SimpleCoordinator{
		tokenManager:    tokenManager,
		receiverManager: receiverManager,
		productManager:  productManager,
		enableMixedMode: false,
		storage:         storage,
	}
}

// ===== 与Balancer兼容的API接口 =====

// AddNewTokenWithCapabilities 创建支持自定义ID和能力的Token
func (sc *SimpleCoordinator) AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token {
	token := sc.tokenManager.AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken, limit, capabilities)

	// 通知ReceiverManager有新Token可用（直接调用）
	if token != nil {
		sc.receiverManager.HandleTokenCreated()
	}

	return token
}

// RemoveToken 删除Token
func (sc *SimpleCoordinator) RemoveToken(tokenID string) bool {
	// 先通知ReceiverManager处理Token删除（直接调用）
	sc.receiverManager.HandleTokenDeleted(tokenID)

	// 然后删除Token
	return sc.tokenManager.RemoveToken(tokenID)
}

// AddNewReceiverWithCapabilities 创建支持自定义ID和能力的Receiver
func (sc *SimpleCoordinator) AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver {
	receiver := sc.receiverManager.AddNewReceiverWithCapabilities(id, capabilities)

	// 通知ProductManager有新Receiver可用（直接调用）
	if receiver != nil {
		sc.productManager.HandleReceiverCreated()
	}

	return receiver
}

// RemoveReceiver 删除Receiver
func (sc *SimpleCoordinator) RemoveReceiver(receiverID string) bool {
	// 先通知ProductManager处理Receiver删除（直接调用）
	sc.productManager.HandleReceiverDeleted(receiverID)

	// 然后删除Receiver
	return sc.receiverManager.RemoveReceiver(receiverID)
}

// AddProduct 添加产品
func (sc *SimpleCoordinator) AddProduct(code, symbol string, productType int) *Product {
	return sc.productManager.AddProduct(code, symbol, productType)
}

// RemoveProduct 删除产品
func (sc *SimpleCoordinator) RemoveProduct(code string) bool {
	return sc.productManager.RemoveProduct(code)
}

// AddProductsBatch 批量添加产品
func (sc *SimpleCoordinator) AddProductsBatch(opt *ProductOpt) []*Product {
	return sc.productManager.AddProductsBatch(opt)
}

// RemoveProductsBatch 批量删除产品
func (sc *SimpleCoordinator) RemoveProductsBatch(opt *ProductOpt) int {
	return sc.productManager.RemoveProductsBatch(opt)
}

// AllocateProduct 分配产品
func (sc *SimpleCoordinator) AllocateProduct(productType int) (receiverID, tokenID string) {
	return sc.productManager.AllocateProduct(productType)
}

// SetMixedMode 设置混用模式
func (sc *SimpleCoordinator) SetMixedMode(enabled bool) {
	if sc.enableMixedMode != enabled {
		sc.enableMixedMode = enabled
		log.Printf("SimpleCoordinator混用模式设置为: %v", enabled)

		// 通知各个管理器（直接调用）
		if sc.receiverManager != nil {
			sc.receiverManager.SetMixedMode(enabled)
		}
		if sc.productManager != nil {
			sc.productManager.SetMixedMode(enabled)
		}
	}
}

// GetCapabilityInfoFromStrings 从能力字符串数组获取能力信息
func (sc *SimpleCoordinator) GetCapabilityInfoFromStrings(capabilities []string) (bits int, description string) {
	bits = calculateCapabilityBitsFromStrings(capabilities)
	description = getCapabilityDescription(bits)
	return bits, description
}

// ===== 数据访问接口 =====

// GetTokens 获取所有Token（兼容Balancer.Tokens字段）
func (sc *SimpleCoordinator) GetTokens() map[string]*Token {
	if sc.tokenManager == nil {
		return make(map[string]*Token)
	}
	return sc.tokenManager.GetAllTokens()
}

// GetReceivers 获取所有Receiver（兼容Balancer.Receivers字段）
func (sc *SimpleCoordinator) GetReceivers() map[string]*Receiver {
	if sc.receiverManager == nil {
		return make(map[string]*Receiver)
	}
	return sc.receiverManager.GetAllReceivers()
}

// GetProducts 获取所有Product（兼容Balancer.Products字段）
func (sc *SimpleCoordinator) GetProducts() map[string]*Product {
	if sc.productManager == nil {
		return make(map[string]*Product)
	}
	return sc.productManager.GetAllProducts()
}

// GetProductsByType 获取按类型分组的产品（兼容Balancer.ProductsByType字段）
func (sc *SimpleCoordinator) GetProductsByType() map[int][]*Product {
	if sc.productManager == nil {
		return make(map[int][]*Product)
	}
	return sc.productManager.GetProductsByType()
}

// GetAvailableReceiversByType 获取按类型分组的可用Receiver（兼容Balancer.AvailableReceiversByType字段）
func (sc *SimpleCoordinator) GetAvailableReceiversByType() map[int][]*Receiver {
	if sc.receiverManager == nil {
		return make(map[int][]*Receiver)
	}
	return sc.receiverManager.GetAvailableReceiversByType()
}

// GetUnbindedTokens 获取未绑定的Token（兼容Balancer.UnbindedTokens字段）
func (sc *SimpleCoordinator) GetUnbindedTokens() map[string]*Token {
	if sc.tokenManager == nil {
		return make(map[string]*Token)
	}
	return sc.tokenManager.GetUnbindedTokens()
}

// GetEnableMixedMode 获取混用模式状态（兼容Balancer.EnableMixedMode字段）
func (sc *SimpleCoordinator) GetEnableMixedMode() bool {
	return sc.enableMixedMode
}

// ===== 简化的同步等待机制 =====

// WaitForEventProcessing 等待事件处理完成（简化版本，立即返回）
func (sc *SimpleCoordinator) WaitForEventProcessing(timeout int) error {
	// 简化版本中没有异步事件处理，直接返回成功
	log.Printf("SimpleCoordinator: 事件处理完成（简化版本无异步处理）")
	return nil
}

// ===== 健康检查和状态查询 =====

// GetStatus 获取系统状态
func (sc *SimpleCoordinator) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"running":           true, // 简化版本始终运行
		"enable_mixed_mode": sc.enableMixedMode,
		"event_bus_running": false, // 简化版本没有事件总线
		"architecture":      "simplified",
	}

	if sc.tokenManager != nil {
		tokens := sc.tokenManager.GetAllTokens()
		unbindedTokens := sc.tokenManager.GetUnbindedTokens()
		status["token_count"] = len(tokens)
		status["unbound_token_count"] = len(unbindedTokens)
	}

	if sc.receiverManager != nil {
		receivers := sc.receiverManager.GetAllReceivers()
		availableReceivers := sc.receiverManager.GetAvailableReceiversByType()
		status["receiver_count"] = len(receivers)

		totalAvailable := 0
		for _, receivers := range availableReceivers {
			totalAvailable += len(receivers)
		}
		status["available_receiver_count"] = totalAvailable
	}

	if sc.productManager != nil {
		products := sc.productManager.GetAllProducts()
		status["product_count"] = len(products)
	}

	return status
}

// ===== 管理器访问接口（用于高级操作） =====

// GetTokenManager 获取TokenManager（用于高级操作）
func (sc *SimpleCoordinator) GetTokenManager() *SimpleTokenManager {
	return sc.tokenManager
}

// GetReceiverManager 获取ReceiverManager（用于高级操作）
func (sc *SimpleCoordinator) GetReceiverManager() *SimpleReceiverManager {
	return sc.receiverManager
}

// GetProductManager 获取ProductManager（用于高级操作）
func (sc *SimpleCoordinator) GetProductManager() *SimpleProductManager {
	return sc.productManager
}

// GetStorage 获取Storage（用于高级操作）
func (sc *SimpleCoordinator) GetStorage() SimpleStorage {
	return sc.storage
}

// ===== 兼容性方法（保持与原Balancer完全一致） =====

// Tokens 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) Tokens() map[string]*Token {
	return sc.GetTokens()
}

// Receivers 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) Receivers() map[string]*Receiver {
	return sc.GetReceivers()
}

// Products 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) Products() map[string]*Product {
	return sc.GetProducts()
}

// ProductsByType 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) ProductsByType() map[int][]*Product {
	return sc.GetProductsByType()
}

// AvailableReceiversByType 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) AvailableReceiversByType() map[int][]*Receiver {
	return sc.GetAvailableReceiversByType()
}

// UnbindedTokens 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) UnbindedTokens() map[string]*Token {
	return sc.GetUnbindedTokens()
}

// EnableMixedMode 属性访问器（兼容直接字段访问）
func (sc *SimpleCoordinator) EnableMixedMode() bool {
	return sc.GetEnableMixedMode()
}

// ===== 系统指标和高级功能 =====

// GetSystemMetrics 获取系统指标
func (sc *SimpleCoordinator) GetSystemMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})

	// Token指标
	if sc.tokenManager != nil {
		tokens := sc.tokenManager.GetAllTokens()
		totalTokenUsage := 0
		totalTokenLimit := 0
		totalConnections := 0

		for _, token := range tokens {
			totalTokenUsage += token.Used
			totalTokenLimit += token.Limit
			totalConnections += token.ConnNum
		}

		usageRate := 0.0
		if totalTokenLimit > 0 {
			usageRate = float64(totalTokenUsage) / float64(totalTokenLimit)
		}

		metrics["token_metrics"] = map[string]interface{}{
			"total_count":       len(tokens),
			"total_usage":       totalTokenUsage,
			"total_limit":       totalTokenLimit,
			"total_connections": totalConnections,
			"usage_rate":        usageRate,
		}
	}

	// Receiver指标
	if sc.receiverManager != nil {
		receivers := sc.receiverManager.GetAllReceivers()
		totalReceiverUsage := 0
		boundReceivers := 0

		for _, receiver := range receivers {
			totalReceiverUsage += receiver.Used
			if receiver.TokenID != "" {
				boundReceivers++
			}
		}

		averageUsage := 0.0
		bindingRate := 0.0
		if len(receivers) > 0 {
			averageUsage = float64(totalReceiverUsage) / float64(len(receivers))
			bindingRate = float64(boundReceivers) / float64(len(receivers))
		}

		metrics["receiver_metrics"] = map[string]interface{}{
			"total_count":   len(receivers),
			"bound_count":   boundReceivers,
			"total_usage":   totalReceiverUsage,
			"average_usage": averageUsage,
			"binding_rate":  bindingRate,
		}
	}

	// Product指标
	if sc.productManager != nil {
		products := sc.productManager.GetAllProducts()
		boundProducts := 0

		for _, product := range products {
			if product.Receiver != nil {
				boundProducts++
			}
		}

		bindingRate := 0.0
		if len(products) > 0 {
			bindingRate = float64(boundProducts) / float64(len(products))
		}

		metrics["product_metrics"] = map[string]interface{}{
			"total_count":  len(products),
			"bound_count":  boundProducts,
			"binding_rate": bindingRate,
		}
	}

	return metrics
}

// ===== 简化的存储操作接口 =====

// StoreData 存储数据到存储后端
func (sc *SimpleCoordinator) StoreData(key string, value interface{}) error {
	if sc.storage == nil {
		return fmt.Errorf("存储未初始化")
	}
	return sc.storage.Set(key, value)
}

// LoadData 从存储后端加载数据
func (sc *SimpleCoordinator) LoadData(key string) (interface{}, error) {
	if sc.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}
	return sc.storage.Get(key)
}

// DeleteData 从存储后端删除数据
func (sc *SimpleCoordinator) DeleteData(key string) error {
	if sc.storage == nil {
		return fmt.Errorf("存储未初始化")
	}
	return sc.storage.Delete(key)
}

// ListData 列出存储后端的数据
func (sc *SimpleCoordinator) ListData(prefix string) (map[string]interface{}, error) {
	if sc.storage == nil {
		return nil, fmt.Errorf("存储未初始化")
	}
	return sc.storage.List(prefix)
}

// ===== 简化的数据持久化方法 =====

// LoadFromStorage 从存储加载所有数据
func (sc *SimpleCoordinator) LoadFromStorage() error {
	if sc.storage == nil {
		return nil // 没有存储，跳过加载
	}

	log.Printf("SimpleCoordinator: 开始从存储加载数据")

	// 加载Token数据
	if sc.tokenManager != nil {
		if err := sc.tokenManager.LoadFromStorage(); err != nil {
			log.Printf("加载Token数据失败: %v", err)
		}
	}

	// 加载Receiver数据
	if sc.receiverManager != nil {
		if err := sc.receiverManager.LoadFromStorage(); err != nil {
			log.Printf("加载Receiver数据失败: %v", err)
		}
	}

	// 加载Product数据
	if sc.productManager != nil {
		if err := sc.productManager.LoadFromStorage(); err != nil {
			log.Printf("加载Product数据失败: %v", err)
		}
	}

	log.Printf("SimpleCoordinator: 数据加载完成")
	return nil
}

// ===== 简化的生命周期管理（兼容性） =====

// Start 启动协调器（简化版本，立即返回成功）
func (sc *SimpleCoordinator) Start(ctx interface{}) error {
	log.Printf("SimpleCoordinator: 启动成功（简化版本）")

	// 如果有存储，加载数据
	if err := sc.LoadFromStorage(); err != nil {
		log.Printf("从存储加载数据失败: %v", err)
	}

	return nil
}

// Stop 停止协调器（简化版本，立即返回成功）
func (sc *SimpleCoordinator) Stop() error {
	log.Printf("SimpleCoordinator: 停止成功（简化版本）")

	// 如果有存储，关闭连接
	if sc.storage != nil {
		if err := sc.storage.Close(); err != nil {
			log.Printf("关闭存储连接失败: %v", err)
		}
	}

	return nil
}

// IsRunning 检查协调器是否运行（简化版本，始终返回true）
func (sc *SimpleCoordinator) IsRunning() bool {
	return true // 简化版本始终运行
}
