<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balance API 管理界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Balance API 管理界面</h1>
        
        <!-- 系统状态 -->
        <div class="section">
            <h2>系统状态</h2>
            <button class="button" onclick="getSystemStatus()">获取系统状态</button>
            <button class="button" onclick="getProductStats()">获取统计信息</button>
            <div id="systemStatus" class="result"></div>
        </div>

        <!-- Token 管理 -->
        <div class="section">
            <h2>Token 管理</h2>
            <div class="input-group">
                <label>ID:</label>
                <input type="text" id="tokenId" placeholder="token-001" required>
            </div>
            <div class="input-group">
                <label>API Key:</label>
                <input type="text" id="tokenApikey" placeholder="your-api-key" required>
            </div>
            <div class="input-group">
                <label>API Secret:</label>
                <input type="text" id="tokenApisecret" placeholder="your-api-secret" required>
            </div>
            <div class="input-group">
                <label>API Token:</label>
                <input type="text" id="tokenApitoken" placeholder="your-api-token" required>
            </div>
            <div class="input-group">
                <label>限额:</label>
                <input type="number" id="tokenLimit" value="1000" min="1">
            </div>
            <div class="input-group">
                <label>能力:</label>
                <input type="text" id="tokenCapability" placeholder="stock_us,stock_a (逗号分隔)" required>
                <small>示例: forex | energy | stock_us,stock_a | stock_hk,stock_a | stock_a | crypto</small>
            </div>
            <button class="button" onclick="createToken()">创建 Token</button>
            <button class="button" onclick="listTokens()">获取所有 Token</button>
            <div id="tokenResult" class="result"></div>
        </div>

        <!-- Receiver 管理 -->
        <div class="section">
            <h2>Receiver 管理</h2>
            <div class="input-group">
                <label>ID:</label>
                <input type="text" id="receiverId" placeholder="receiver-001" required>
            </div>
            <div class="input-group">
                <label>能力 (可选):</label>
                <input type="text" id="receiverCapability" placeholder="stock_us,stock_a (逗号分隔，留空默认stock_a)">
                <small>示例: forex | energy | stock_us,stock_a | stock_hk,stock_a | stock_a | crypto</small>
            </div>
            <button class="button" onclick="createReceiver()">创建 Receiver</button>
            <button class="button" onclick="listReceivers()">获取所有 Receiver</button>
            <div id="receiverResult" class="result"></div>
        </div>

        <!-- Product 管理 -->
        <div class="section">
            <h2>Product 管理</h2>
            <div class="input-group">
                <label>产品代码:</label>
                <input type="text" id="productCode" placeholder="AAPL" required>
            </div>
            <div class="input-group">
                <label>产品符号:</label>
                <input type="text" id="productSymbol" placeholder="Apple Inc." required>
                <small id="symbolHint" style="color: #666; display: none;">股票类型产品的符号将自动与代码相同</small>
            </div>
            <div class="input-group">
                <label>产品类型:</label>
                <select id="productType">
                    <option value="1">1 - forex</option>
                    <option value="2">2 - energy</option>
                    <option value="3">3 - stock_us</option>
                    <option value="4">4 - stock_hk</option>
                    <option value="5">5 - stock_a</option>
                    <option value="6">6 - crypto</option>
                </select>
            </div>
            <button class="button" onclick="createProduct()">创建 Product</button>
            <button class="button" onclick="listProducts()">获取所有 Product</button>
            <button class="button" onclick="allocateProduct()">分配产品</button>
            <div id="productResult" class="result"></div>
        </div>

        <!-- 系统配置 -->
        <div class="section">
            <h2>系统配置</h2>
            <button class="button" onclick="enableMixedMode()">启用混用模式</button>
            <button class="button" onclick="disableMixedMode()">禁用混用模式</button>
            <!-- 能力查询功能已移除 -->
            <div id="configResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';

        async function apiCall(method, endpoint, data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(API_BASE + endpoint, options);
                const result = await response.json();
                
                return {
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    status: 0,
                    data: { code: 0, message: 'Network error: ' + error.message }
                };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const statusClass = result.status >= 200 && result.status < 300 ? 'success' : 'error';
            element.innerHTML = `<span class="status ${statusClass}">Status: ${result.status}</span>\n${JSON.stringify(result.data, null, 2)}`;
        }

        // 系统状态相关
        async function getSystemStatus() {
            const result = await apiCall('GET', '/system/status');
            displayResult('systemStatus', result);
        }

        async function getProductStats() {
            const result = await apiCall('GET', '/products/stats');
            displayResult('systemStatus', result);
        }

        // Token 相关
        async function createToken() {
            const id = document.getElementById('tokenId').value;
            const apikey = document.getElementById('tokenApikey').value;
            const apisecret = document.getElementById('tokenApisecret').value;
            const apitoken = document.getElementById('tokenApitoken').value;
            const limit = parseInt(document.getElementById('tokenLimit').value);
            const capabilityText = document.getElementById('tokenCapability').value;

            const data = {
                id: id,
                apikey: apikey,
                apisecret: apisecret,
                apitoken: apitoken,
                limit: limit,
                capability: capabilityText.split(',').map(s => s.trim())
            };

            const result = await apiCall('POST', '/tokens', data);
            displayResult('tokenResult', result);
        }

        async function listTokens() {
            const result = await apiCall('GET', '/tokens');
            displayResult('tokenResult', result);
        }

        // Receiver 相关
        async function createReceiver() {
            const id = document.getElementById('receiverId').value;
            const capabilityText = document.getElementById('receiverCapability').value;

            const data = { id: id };
            if (capabilityText) {
                data.capability = capabilityText.split(',').map(s => s.trim());
            }

            const result = await apiCall('POST', '/receivers', data);
            displayResult('receiverResult', result);
        }

        async function listReceivers() {
            const result = await apiCall('GET', '/receivers');
            displayResult('receiverResult', result);
        }

        // Product 相关
        async function createProduct() {
            const code = document.getElementById('productCode').value;
            const symbol = document.getElementById('productSymbol').value;
            const type = parseInt(document.getElementById('productType').value);

            // 验证输入
            if (!code.trim()) {
                alert('产品代码不能为空');
                return;
            }

            if (!symbol.trim()) {
                alert('产品符号不能为空');
                return;
            }

            const result = await apiCall('POST', '/products', {
                code: code.trim(),
                symbol: symbol.trim(),
                type: type
            });
            displayResult('productResult', result);
        }

        async function listProducts() {
            const result = await apiCall('GET', '/products');
            displayResult('productResult', result);
        }

        // 产品分配
        async function allocateProduct() {
            const code = document.getElementById('productCode').value;
            const type = parseInt(document.getElementById('productType').value);

            const result = await apiCall('POST', '/products/allocate', {
                code: code,
                type: type
            });
            displayResult('productResult', result);
        }

        // 系统配置
        async function enableMixedMode() {
            const result = await apiCall('PUT', '/system/mixed-mode', {
                enable_mixed_mode: true
            });
            displayResult('configResult', result);
        }

        async function disableMixedMode() {
            const result = await apiCall('PUT', '/system/mixed-mode', {
                enable_mixed_mode: false
            });
            displayResult('configResult', result);
        }

        // getCapabilities 函数已移除

        // 产品类型变化时的处理
        function handleProductTypeChange() {
            const productType = parseInt(document.getElementById('productType').value);
            const productCode = document.getElementById('productCode');
            const productSymbol = document.getElementById('productSymbol');
            const symbolHint = document.getElementById('symbolHint');

            // 检查是否为股票类型 (3, 4, 5)
            const isStockType = productType >= 3 && productType <= 5;

            if (isStockType) {
                // 股票类型：符号与代码相同，不可修改
                productSymbol.value = productCode.value;
                productSymbol.readOnly = true;
                productSymbol.style.backgroundColor = '#f5f5f5';
                symbolHint.style.display = 'block';
            } else {
                // 非股票类型：可以修改符号
                productSymbol.readOnly = false;
                productSymbol.style.backgroundColor = '';
                symbolHint.style.display = 'none';
            }
        }

        // 产品代码变化时的处理
        function handleProductCodeChange() {
            const productType = parseInt(document.getElementById('productType').value);
            const productCode = document.getElementById('productCode');
            const productSymbol = document.getElementById('productSymbol');

            // 如果是股票类型，自动同步符号
            const isStockType = productType >= 3 && productType <= 5;
            if (isStockType) {
                productSymbol.value = productCode.value;
            }
        }

        // 页面加载时获取系统状态并设置事件监听器
        window.onload = function() {
            getSystemStatus();

            // 添加事件监听器
            document.getElementById('productType').addEventListener('change', handleProductTypeChange);
            document.getElementById('productCode').addEventListener('input', handleProductCodeChange);

            // 初始化状态
            handleProductTypeChange();
        };
    </script>
</body>
</html>
