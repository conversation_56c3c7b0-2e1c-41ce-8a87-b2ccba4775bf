# Balance API 使用示例

## 启动服务器

```bash
go mod tidy
go run main_api.go balance.go api.go
```

服务器将在 `http://localhost:8080` 启动

## API 接口示例

### 1. Token 管理

#### 创建单类型 Token
```bash
curl -X POST http://localhost:8080/api/v1/tokens \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 1000,
    "original_type": 3
  }'
```

#### 创建多类型组合 Token (stock_us + stock_hk)
```bash
curl -X POST http://localhost:8080/api/v1/tokens \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 1500,
    "original_type": 3,
    "types": [3, 4]
  }'
```

#### 获取所有 Token
```bash
curl http://localhost:8080/api/v1/tokens
```

#### 获取指定 Token
```bash
curl http://localhost:8080/api/v1/tokens/token-1
```

#### 更新 Token 限额
```bash
curl -X PUT http://localhost:8080/api/v1/tokens/token-1 \
  -H "Content-Type: application/json" \
  -d '{
    "limit": 1200
  }'
```

#### 删除 Token
```bash
curl -X DELETE http://localhost:8080/api/v1/tokens/token-1
```

### 2. Receiver 管理

#### 创建指定类型的 Receiver
```bash
curl -X POST http://localhost:8080/api/v1/receivers \
  -H "Content-Type: application/json" \
  -d '{
    "original_type": 3
  }'
```

#### 创建默认类型的 Receiver (stock_a)
```bash
curl -X POST http://localhost:8080/api/v1/receivers \
  -H "Content-Type: application/json" \
  -d '{}'
```

#### 获取所有 Receiver
```bash
curl http://localhost:8080/api/v1/receivers
```

#### 获取指定 Receiver
```bash
curl http://localhost:8080/api/v1/receivers/receiver-1
```

#### 更新 Receiver 使用量
```bash
curl -X PUT http://localhost:8080/api/v1/receivers/receiver-1 \
  -H "Content-Type: application/json" \
  -d '{
    "used": 100
  }'
```

#### 删除 Receiver
```bash
curl -X DELETE http://localhost:8080/api/v1/receivers/receiver-1
```

### 3. Product 管理

#### 分配产品
```bash
# 分配 stock_us 产品
curl -X POST http://localhost:8080/api/v1/products/allocate \
  -H "Content-Type: application/json" \
  -d '{
    "product_type": 3
  }'

# 分配 stock_a 产品
curl -X POST http://localhost:8080/api/v1/products/allocate \
  -H "Content-Type: application/json" \
  -d '{
    "product_type": 5
  }'
```

#### 获取产品统计信息
```bash
curl http://localhost:8080/api/v1/products/stats
```

### 4. 系统配置

#### 获取系统状态
```bash
curl http://localhost:8080/api/v1/system/status
```

#### 启用混用模式
```bash
curl -X PUT http://localhost:8080/api/v1/system/mixed-mode \
  -H "Content-Type: application/json" \
  -d '{
    "enable_mixed_mode": true
  }'
```

#### 禁用混用模式
```bash
curl -X PUT http://localhost:8080/api/v1/system/mixed-mode \
  -H "Content-Type: application/json" \
  -d '{
    "enable_mixed_mode": false
  }'
```

#### 获取指定类型的能力信息
```bash
# 获取 stock_us 的能力信息
curl http://localhost:8080/api/v1/system/capabilities/3

# 获取 stock_hk 的能力信息
curl http://localhost:8080/api/v1/system/capabilities/4
```

## 产品类型说明

- `1`: forex (外汇)
- `2`: energy (能源)
- `3`: stock_us (美股，支持 stock_us + stock_a)
- `4`: stock_hk (港股，支持 stock_hk + stock_a)
- `5`: stock_a (A股，只支持 stock_a)
- `6`: crypto (加密货币)

## 响应格式

所有 API 响应都遵循统一格式：

```json
{
  "code": 200,
  "message": "操作成功信息",
  "data": {
    // 具体数据
  }
}
```

## 错误处理

- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 完整测试流程示例

```bash
# 1. 创建 Token
curl -X POST http://localhost:8080/api/v1/tokens \
  -H "Content-Type: application/json" \
  -d '{"limit": 1000, "original_type": 3}'

# 2. 创建 Receiver
curl -X POST http://localhost:8080/api/v1/receivers \
  -H "Content-Type: application/json" \
  -d '{"original_type": 3}'

# 3. 分配产品
curl -X POST http://localhost:8080/api/v1/products/allocate \
  -H "Content-Type: application/json" \
  -d '{"product_type": 3}'

# 4. 查看统计信息
curl http://localhost:8080/api/v1/products/stats

# 5. 查看系统状态
curl http://localhost:8080/api/v1/system/status
```
