package main

import (
	"fmt"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
)

// API 响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Token 请求结构
type TokenRequest struct {
	ID         string   `json:"id" binding:"required"`
	Apikey     string   `json:"apikey" binding:"required"`
	ApiSecret  string   `json:"apisecret" binding:"required"`
	ApiToken   string   `json:"apitoken" binding:"required"`
	Limit      int      `json:"limit" binding:"min=1"`
	Capability []string `json:"capability" binding:"required"`
}

// Receiver 请求结构
type ReceiverRequest struct {
	ID         string   `json:"id" binding:"required"`
	Capability []string `json:"capability,omitempty"` // 可选，不传则默认为stock_a
}

// Product 请求结构
type ProductRequest struct {
	Code   string `json:"code" binding:"required"`
	Symbol string `json:"symbol,omitempty"`
	Type   int    `json:"type" binding:"required,min=1,max=6"`
}

// Product 批量操作请求结构（使用 ProductOpt）
type ProductOptRequest struct {
	Type  int       `json:"type" binding:"required,min=1,max=6"`
	Codes []*string `json:"codes" binding:"required"`
}

// ===== 版本选择和统一接口 =====

// CoordinatorInterface 统一的协调器接口，支持两种实现
type CoordinatorInterface interface {
	// Token 管理
	AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token
	RemoveToken(tokenID string) bool
	GetTokens() map[string]*Token
	GetUnbindedTokens() map[string]*Token

	// Receiver 管理
	AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver
	RemoveReceiver(receiverID string) bool
	GetReceivers() map[string]*Receiver
	GetAvailableReceiversByType() map[int][]*Receiver

	// Product 管理
	AddProduct(code, symbol string, productType int) *Product
	RemoveProduct(code string) bool
	AddProductsBatch(opt *ProductOpt) []*Product
	RemoveProductsBatch(opt *ProductOpt) int
	AllocateProduct(productType int) (receiverID, tokenID string)
	GetProducts() map[string]*Product
	GetProductsByType() map[int][]*Product

	// 系统配置
	SetMixedMode(enabled bool)
	GetEnableMixedMode() bool
	GetCapabilityInfoFromStrings(capabilities []string) (bits int, description string)

	// 状态查询
	GetStatus() map[string]interface{}
	GetSystemMetrics() map[string]interface{}
	WaitForEventProcessing(timeout int) error

	// 管理器访问（用于高级操作）
	GetTokenManager() interface{}
	GetReceiverManager() interface{}
	GetProductManager() interface{}
	GetStorage() interface{}
}

// 架构版本类型
type ArchitectureVersion string

const (
	ArchitectureComplex    ArchitectureVersion = "complex"
	ArchitectureSimplified ArchitectureVersion = "simplified"
)

// 全局变量
var (
	globalCoordinator     CoordinatorInterface
	currentArchitecture   ArchitectureVersion
	complexCoordinator    *Coordinator
	simplifiedCoordinator *SimpleCoordinator
)

// 获取当前架构版本（从环境变量或默认值）
func getCurrentArchitecture() ArchitectureVersion {
	archEnv := os.Getenv("ARCHITECTURE_VERSION")
	switch archEnv {
	case "complex":
		return ArchitectureComplex
	case "simplified":
		return ArchitectureSimplified
	default:
		// 默认使用简化版本
		return ArchitectureSimplified
	}
}

// 初始化协调器（根据版本选择）
func initializeCoordinator() CoordinatorInterface {
	currentArchitecture = getCurrentArchitecture()

	switch currentArchitecture {
	case ArchitectureComplex:
		complexCoordinator = NewCoordinator()
		return complexCoordinator
	case ArchitectureSimplified:
		simplifiedCoordinator = NewSimpleCoordinator()
		return simplifiedCoordinator
	default:
		// 默认使用简化版本
		simplifiedCoordinator = NewSimpleCoordinator()
		currentArchitecture = ArchitectureSimplified
		return simplifiedCoordinator
	}
}

// 初始化 API 路由
func setupRoutes() *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// CORS 中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 静态文件服务
	r.Static("/static", "./")
	r.StaticFile("/", "./index.html")

	// API 版本分组
	v1 := r.Group("/api/v1")
	{
		// Token 相关路由
		tokens := v1.Group("/tokens")
		{
			tokens.POST("", createToken)
			tokens.GET("", listTokens)
			tokens.GET("/:id", getToken)
			tokens.PUT("/:id", updateToken)
			tokens.DELETE("/:id", deleteToken)
		}

		// Receiver 相关路由
		receivers := v1.Group("/receivers")
		{
			receivers.POST("", createReceiver)
			receivers.GET("", listReceivers)
			receivers.GET("/:id", getReceiver)
			receivers.PUT("/:id", updateReceiver)
			receivers.DELETE("/:id", deleteReceiver)
		}

		// Product 相关路由
		products := v1.Group("/products")
		{
			products.POST("", createProduct)
			products.GET("", listProducts)
			products.GET("/:code", getProduct)
			products.PUT("/:code", updateProduct)
			products.DELETE("/:code", deleteProduct)
			products.POST("/allocate", allocateProduct)
			products.POST("/batch", batchCreateProducts)
			products.DELETE("/batch", batchDeleteProducts)
			products.GET("/stats", getProductStats)
		}

		// 系统配置路由
		system := v1.Group("/system")
		{
			system.GET("/status", getSystemStatus)
			system.GET("/health", getArchitectureHealth)
			system.PUT("/mixed-mode", setMixedMode)
		}
	}

	return r
}

// Token CRUD 操作

// 创建 Token
func createToken(c *gin.Context) {
	var req TokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	// 检查 Token ID 是否已存在
	tokens := globalCoordinator.GetTokens()
	if _, exists := tokens[req.ID]; exists {
		c.JSON(http.StatusConflict, APIResponse{
			Code:    409,
			Message: "Token ID already exists",
		})
		return
	}

	// 设置默认值
	if req.Limit == 0 {
		req.Limit = 500
	}
	if len(req.Capability) == 0 {
		req.Capability = []string{"stock_a"} // 默认 stock_a
	}

	// 使用新的基于能力字符串的创建函数
	token := globalCoordinator.AddNewTokenWithCapabilities(req.ID, req.Apikey, req.ApiSecret, req.ApiToken, req.Limit, req.Capability)

	// 构建响应消息
	message := "Token created successfully"
	if token.ConnNum > 0 {
		message += fmt.Sprintf(", automatically bound to %d receiver(s)", token.ConnNum)
	} else {
		message += ", waiting for compatible receivers"
	}

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: message,
		Data: gin.H{
			"id":         token.ID,
			"apikey":     token.Apikey,
			"apisecret":  token.ApiSecret,
			"apitoken":   token.ApiToken,
			"limit":      token.Limit,
			"used":       token.Used,
			"conn_num":   token.ConnNum,
			"capability": token.Capability,
		},
	})
}

// 获取所有 Token
func listTokens(c *gin.Context) {
	var tokens []gin.H
	for _, token := range globalCoordinator.GetTokens() {
		tokens = append(tokens, gin.H{
			"id":         token.ID,
			"apikey":     token.Apikey,
			"apisecret":  token.ApiSecret,
			"apitoken":   token.ApiToken,
			"limit":      token.Limit,
			"used":       token.Used,
			"conn_num":   token.ConnNum,
			"capability": token.Capability,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Tokens retrieved successfully",
		Data:    tokens,
	})
}

// 获取单个 Token
func getToken(c *gin.Context) {
	tokenID := c.Param("id")
	tokens := globalCoordinator.GetTokens()
	token, exists := tokens[tokenID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token retrieved successfully",
		Data: gin.H{
			"id":         token.ID,
			"apikey":     token.Apikey,
			"apisecret":  token.ApiSecret,
			"apitoken":   token.ApiToken,
			"limit":      token.Limit,
			"used":       token.Used,
			"conn_num":   token.ConnNum,
			"capability": token.Capability,
		},
	})
}

// 更新 Token
func updateToken(c *gin.Context) {
	tokenID := c.Param("id")
	tokens := globalCoordinator.GetTokens()
	token, exists := tokens[tokenID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	var req struct {
		Limit *int `json:"limit,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	if req.Limit != nil && *req.Limit > 0 {
		token.Limit = *req.Limit
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token updated successfully",
		Data: gin.H{
			"id":    token.ID,
			"limit": token.Limit,
			"used":  token.Used,
		},
	})
}

// 删除 Token
func deleteToken(c *gin.Context) {
	tokenID := c.Param("id")
	if !globalCoordinator.RemoveToken(tokenID) {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Token not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Token deleted successfully, related receivers have been rebound",
	})
}

// Receiver CRUD 操作

// 创建 Receiver
func createReceiver(c *gin.Context) {
	var req ReceiverRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	// 检查 Receiver ID 是否已存在
	receivers := globalCoordinator.GetReceivers()
	if _, exists := receivers[req.ID]; exists {
		c.JSON(http.StatusConflict, APIResponse{
			Code:    409,
			Message: "Receiver ID already exists",
		})
		return
	}

	// 使用新的基于能力字符串的创建函数
	receiver := globalCoordinator.AddNewReceiverWithCapabilities(req.ID, req.Capability)

	// 构建响应消息
	message := "Receiver created successfully"
	if receiver.TokenID != "" {
		message += fmt.Sprintf(", automatically bound to token %s", receiver.TokenID)
	} else {
		message += ", waiting for compatible token"
	}

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: message,
		Data: gin.H{
			"id":         receiver.ID,
			"token_id":   receiver.TokenID,
			"used":       receiver.Used,
			"capability": receiver.Capability,
		},
	})
}

// 获取所有 Receiver
func listReceivers(c *gin.Context) {
	var receivers []gin.H
	for _, receiver := range globalCoordinator.GetReceivers() {
		receivers = append(receivers, gin.H{
			"id":         receiver.ID,
			"token_id":   receiver.TokenID,
			"used":       receiver.Used,
			"capability": receiver.Capability,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receivers retrieved successfully",
		Data:    receivers,
	})
}

// 获取单个 Receiver
func getReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	receivers := globalCoordinator.GetReceivers()
	receiver, exists := receivers[receiverID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver retrieved successfully",
		Data: gin.H{
			"id":         receiver.ID,
			"token_id":   receiver.TokenID,
			"used":       receiver.Used,
			"capability": receiver.Capability,
		},
	})
}

// 更新 Receiver
func updateReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	receivers := globalCoordinator.GetReceivers()
	receiver, exists := receivers[receiverID]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	var req struct {
		Used *int `json:"used,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	if req.Used != nil && *req.Used >= 0 {
		// 通过ReceiverManager更新使用量
		receiverManager := globalCoordinator.GetReceiverManager()
		if receiverManager != nil {
			delta := *req.Used - receiver.Used
			var err error

			// 根据架构版本进行类型断言
			switch currentArchitecture {
			case ArchitectureComplex:
				if rm, ok := receiverManager.(*ReceiverManager); ok {
					err = rm.UpdateReceiverUsage(receiverID, delta)
				}
			case ArchitectureSimplified:
				if rm, ok := receiverManager.(*SimpleReceiverManager); ok {
					err = rm.UpdateReceiverUsage(receiverID, delta)
				}
			}

			if err != nil {
				c.JSON(http.StatusInternalServerError, APIResponse{
					Code:    500,
					Message: "Failed to update receiver usage: " + err.Error(),
				})
				return
			}
		}
	}

	// 重新获取更新后的receiver信息
	receivers = globalCoordinator.GetReceivers()
	receiver = receivers[receiverID]

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver updated successfully",
		Data: gin.H{
			"id":   receiver.ID,
			"used": receiver.Used,
		},
	})
}

// 删除 Receiver
func deleteReceiver(c *gin.Context) {
	receiverID := c.Param("id")
	if !globalCoordinator.RemoveReceiver(receiverID) {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Receiver not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Receiver deleted successfully, token connection count updated",
	})
}

// Product 相关操作

// 创建产品
func createProduct(c *gin.Context) {
	var req ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	// 检查产品代码是否已存在
	products := globalCoordinator.GetProducts()
	if _, exists := products[req.Code]; exists {
		c.JSON(http.StatusConflict, APIResponse{
			Code:    409,
			Message: "Product code already exists",
		})
		return
	}

	product := globalCoordinator.AddProduct(req.Code, req.Symbol, req.Type)

	// 构建响应消息
	message := "Product created successfully"
	if product.Receiver != nil {
		message += fmt.Sprintf(", automatically bound to receiver %s", product.Receiver.ID)
	} else {
		message += ", waiting for available receiver"
	}

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: message,
		Data: gin.H{
			"code":     product.Code,
			"symbol":   product.Symbol,
			"type":     product.Type,
			"receiver": product.Receiver,
		},
	})
}

// 获取所有产品
func listProducts(c *gin.Context) {
	var products []gin.H
	for _, product := range globalCoordinator.GetProducts() {
		products = append(products, gin.H{
			"code":     product.Code,
			"symbol":   product.Symbol,
			"type":     product.Type,
			"receiver": product.Receiver,
		})
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Products retrieved successfully",
		Data:    products,
	})
}

// 获取单个产品
func getProduct(c *gin.Context) {
	code := c.Param("code")
	products := globalCoordinator.GetProducts()
	product, exists := products[code]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Product not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product retrieved successfully",
		Data: gin.H{
			"code":     product.Code,
			"symbol":   product.Symbol,
			"type":     product.Type,
			"receiver": product.Receiver,
		},
	})
}

// 更新产品
func updateProduct(c *gin.Context) {
	code := c.Param("code")
	products := globalCoordinator.GetProducts()
	product, exists := products[code]
	if !exists {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Product not found",
		})
		return
	}

	var req struct {
		Symbol *string `json:"symbol,omitempty"`
		Type   *int    `json:"type,omitempty"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	if req.Symbol != nil {
		product.Symbol = *req.Symbol
	}
	if req.Type != nil && *req.Type >= 1 && *req.Type <= 6 {
		product.Type = *req.Type
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product updated successfully",
		Data: gin.H{
			"code":   product.Code,
			"symbol": product.Symbol,
			"type":   product.Type,
		},
	})
}

// 删除产品
func deleteProduct(c *gin.Context) {
	code := c.Param("code")
	if !globalCoordinator.RemoveProduct(code) {
		c.JSON(http.StatusNotFound, APIResponse{
			Code:    404,
			Message: "Product not found",
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product deleted successfully",
	})
}

// 批量创建产品（使用 ProductOpt）
func batchCreateProducts(c *gin.Context) {
	var req ProductOptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	// 转换为 ProductOpt 结构
	productOpt := &ProductOpt{
		Type:  req.Type,
		Codes: req.Codes,
	}

	// 使用 ProductOpt 进行批量创建
	products := globalCoordinator.AddProductsBatch(productOpt)

	var responseData []gin.H
	boundCount := 0
	for _, product := range products {
		data := gin.H{
			"code":   product.Code,
			"symbol": product.Symbol,
			"type":   product.Type,
		}
		if product.Receiver != nil {
			data["receiver"] = gin.H{
				"id":       product.Receiver.ID,
				"token_id": product.Receiver.TokenID,
				"used":     product.Receiver.Used,
			}
			boundCount++
		}
		responseData = append(responseData, data)
	}

	message := fmt.Sprintf("Successfully created %d products", len(products))
	if boundCount > 0 {
		message += fmt.Sprintf(", %d automatically bound to receivers", boundCount)
	}

	c.JSON(http.StatusCreated, APIResponse{
		Code:    201,
		Message: message,
		Data:    responseData,
	})
}

// 批量删除产品（使用 ProductOpt）
func batchDeleteProducts(c *gin.Context) {
	var req ProductOptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	// 转换为 ProductOpt 结构
	productOpt := &ProductOpt{
		Type:  req.Type,
		Codes: req.Codes,
	}

	// 使用 ProductOpt 进行批量删除
	deletedCount := globalCoordinator.RemoveProductsBatch(productOpt)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: fmt.Sprintf("Successfully deleted %d products", deletedCount),
		Data: gin.H{
			"deleted_count": deletedCount,
			"total_count":   len(req.Codes),
		},
	})
}

// 分配产品
func allocateProduct(c *gin.Context) {
	var req ProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	receiverID, tokenID := globalCoordinator.AllocateProduct(req.Type)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product allocated successfully",
		Data: gin.H{
			"receiver_id":  receiverID,
			"token_id":     tokenID,
			"product_code": req.Code,
			"product_type": req.Type,
		},
	})
}

// 获取产品统计信息
func getProductStats(c *gin.Context) {
	tokens := globalCoordinator.GetTokens()
	receivers := globalCoordinator.GetReceivers()
	unbindedTokens := globalCoordinator.GetUnbindedTokens()
	availableReceiversByType := globalCoordinator.GetAvailableReceiversByType()

	stats := gin.H{
		"total_tokens":                len(tokens),
		"total_receivers":             len(receivers),
		"unbinded_tokens":             len(unbindedTokens),
		"unbinded_receivers":          0, // 计算未绑定的receivers
		"available_receivers_by_type": make(map[string]int),
		"token_usage":                 make([]gin.H, 0),
		"receiver_usage":              make([]gin.H, 0),
	}

	// 计算未绑定的receivers数量
	unbindedReceivers := 0
	for _, receiver := range receivers {
		if receiver.TokenID == "" {
			unbindedReceivers++
		}
	}
	stats["unbinded_receivers"] = unbindedReceivers

	// 统计按类型分组的可用receiver数量
	availableByType := stats["available_receivers_by_type"].(map[string]int)
	for typeNum, receivers := range availableReceiversByType {
		availableByType[strconv.Itoa(typeNum)] = len(receivers)
	}

	// Token使用情况统计
	tokenUsage := stats["token_usage"].([]gin.H)
	for _, token := range tokens {
		tokenUsage = append(tokenUsage, gin.H{
			"id":         token.ID,
			"used":       token.Used,
			"limit":      token.Limit,
			"usage_rate": float64(token.Used) / float64(token.Limit),
			"conn_num":   token.ConnNum,
			"capability": token.Capability,
		})
	}
	stats["token_usage"] = tokenUsage

	// Receiver使用情况统计
	receiverUsage := stats["receiver_usage"].([]gin.H)
	for _, receiver := range receivers {
		receiverUsage = append(receiverUsage, gin.H{
			"id":         receiver.ID,
			"used":       receiver.Used,
			"usage_rate": float64(receiver.Used) / 500.0,
			"token_id":   receiver.TokenID,
		})
	}
	stats["receiver_usage"] = receiverUsage

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Product stats retrieved successfully",
		Data:    stats,
	})
}

// 系统配置相关操作

// 获取系统状态
func getSystemStatus(c *gin.Context) {
	// 使用新的Coordinator状态接口
	systemStatus := globalCoordinator.GetStatus()

	status := gin.H{
		"mixed_mode":          globalCoordinator.GetEnableMixedMode(),
		"total_tokens":        systemStatus["token_count"],
		"total_receivers":     systemStatus["receiver_count"],
		"unbinded_tokens":     systemStatus["unbound_token_count"],
		"unbinded_receivers":  0, // 计算未绑定的receivers
		"system_health":       "healthy",
		"coordinator_running": systemStatus["running"],
		"event_bus_running":   systemStatus["event_bus_running"],
	}

	// 计算未绑定的receivers数量
	receivers := globalCoordinator.GetReceivers()
	unbindedReceivers := 0
	for _, receiver := range receivers {
		if receiver.TokenID == "" {
			unbindedReceivers++
		}
	}
	status["unbinded_receivers"] = unbindedReceivers

	// 计算系统负载
	totalUsed := 0
	totalLimit := 0
	tokens := globalCoordinator.GetTokens()
	for _, token := range tokens {
		totalUsed += token.Used
		totalLimit += token.Limit
	}

	if totalLimit > 0 {
		status["overall_usage_rate"] = float64(totalUsed) / float64(totalLimit)
	} else {
		status["overall_usage_rate"] = 0.0
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "System status retrieved successfully",
		Data:    status,
	})
}

// 设置混用模式
func setMixedMode(c *gin.Context) {
	var req struct {
		EnableMixedMode bool `json:"enable_mixed_mode"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "Invalid request: " + err.Error(),
		})
		return
	}

	globalCoordinator.SetMixedMode(req.EnableMixedMode)

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Mixed mode updated successfully",
		Data: gin.H{
			"enable_mixed_mode": globalCoordinator.GetEnableMixedMode(),
		},
	})
}

// 已移除：不再支持基于 originalType 的能力信息查询

// 获取新架构健康状态
func getArchitectureHealth(c *gin.Context) {
	if globalCoordinator == nil {
		c.JSON(http.StatusServiceUnavailable, APIResponse{
			Code:    503,
			Message: "Coordinator not initialized",
		})
		return
	}

	// 获取系统指标
	metrics := globalCoordinator.GetSystemMetrics()
	status := globalCoordinator.GetStatus()

	health := gin.H{
		"architecture": "event-driven",
		"coordinator":  status,
		"metrics":      metrics,
		"managers": gin.H{
			"token_manager":    globalCoordinator.GetTokenManager() != nil,
			"receiver_manager": globalCoordinator.GetReceiverManager() != nil,
			"product_manager":  globalCoordinator.GetProductManager() != nil,
		},
		"storage": gin.H{
			"type":      "memory", // 当前使用内存存储
			"connected": getStorageConnectionStatus(),
		},
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "Architecture health retrieved successfully",
		Data:    health,
	})
}

// 启动 API 服务器
func StartAPIServer(coordinator *Coordinator, port string) {
	globalCoordinator = coordinator

	r := setupRoutes()

	// 启动服务器
	r.Run(":" + port)
}
