package main

import (
	"context"
	"fmt"
	"log"
	"sync"
)

// TokenData 事件数据结构
type TokenData struct {
	Token           *Token   `json:"token"`
	UnboundReceivers []string `json:"unbound_receivers,omitempty"`
}

// TokenManager Token资源管理器
type TokenManager struct {
	tokens         map[string]*Token
	unbindedTokens map[string]*Token
	eventBus       EventBus
	mu             sync.RWMutex
	ctx            context.Context
	cancel         context.CancelFunc
	running        bool
}

// NewTokenManager 创建Token管理器
func NewTokenManager(eventBus EventBus) *TokenManager {
	return &TokenManager{
		tokens:         make(map[string]*Token),
		unbindedTokens: make(map[string]*Token),
		eventBus:       eventBus,
	}
}

// GetName 获取管理器名称
func (tm *TokenManager) GetName() string {
	return "TokenManager"
}

// Start 启动Token管理器
func (tm *TokenManager) Start(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.running {
		return fmt.Errorf("TokenManager已在运行")
	}

	tm.ctx, tm.cancel = context.WithCancel(ctx)
	tm.running = true

	// 订阅相关事件
	if err := tm.eventBus.Subscribe(ReceiverCreated, tm.HandleEvent); err != nil {
		return fmt.Errorf("订阅ReceiverCreated事件失败: %v", err)
	}
	if err := tm.eventBus.Subscribe(ReceiverDeleted, tm.HandleEvent); err != nil {
		return fmt.Errorf("订阅ReceiverDeleted事件失败: %v", err)
	}
	if err := tm.eventBus.Subscribe(BindingRequested, tm.HandleEvent); err != nil {
		return fmt.Errorf("订阅BindingRequested事件失败: %v", err)
	}

	log.Printf("TokenManager启动成功")
	return nil
}

// Stop 停止Token管理器
func (tm *TokenManager) Stop() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if !tm.running {
		return nil
	}

	tm.running = false
	if tm.cancel != nil {
		tm.cancel()
	}

	log.Printf("TokenManager停止成功")
	return nil
}

// HandleEvent 处理事件
func (tm *TokenManager) HandleEvent(event *Event) error {
	switch event.Type {
	case ReceiverCreated:
		return tm.handleReceiverCreated(event)
	case ReceiverDeleted:
		return tm.handleReceiverDeleted(event)
	case BindingRequested:
		return tm.handleBindingRequested(event)
	default:
		return nil
	}
}

// AddNewTokenWithCapabilities 创建支持自定义ID和能力的Token
func (tm *TokenManager) AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 应用特殊规则
	capabilities = applySpecialRulesToCapabilities(capabilities)

	token := &Token{
		ID:             id,
		Apikey:         apikey,
		ApiSecret:      apisecret,
		ApiToken:       apitoken,
		Limit:          limit,
		Used:           0,
		ConnNum:        0,
		Capability:     capabilities,
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}

	tm.tokens[id] = token
	log.Printf("创建 Token: %s (产品支持类型: %v, 限额: %d)", id, capabilities, limit)

	// 发布Token创建事件
	tokenData := &TokenData{Token: token}
	event := NewEvent(TokenCreated, tokenData, tm.GetName())
	if err := tm.eventBus.Publish(event); err != nil {
		log.Printf("发布TokenCreated事件失败: %v", err)
	}

	// 尝试绑定到可用的Receiver
	tm.tryBindToken(id)

	return token
}

// RemoveToken 删除Token
func (tm *TokenManager) RemoveToken(tokenID string) bool {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	token, exists := tm.tokens[tokenID]
	if !exists {
		return false
	}

	log.Printf("删除 Token: %s (能力: %v)", tokenID, token.Capability)

	// 发布Token删除事件，通知其他管理器解绑相关资源
	tokenData := &TokenData{Token: token}
	event := NewEvent(TokenDeleted, tokenData, tm.GetName())
	if err := tm.eventBus.Publish(event); err != nil {
		log.Printf("发布TokenDeleted事件失败: %v", err)
	}

	// 从本地存储中删除
	delete(tm.unbindedTokens, tokenID)
	delete(tm.tokens, tokenID)

	return true
}

// GetToken 获取Token
func (tm *TokenManager) GetToken(tokenID string) (*Token, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	token, exists := tm.tokens[tokenID]
	return token, exists
}

// GetAllTokens 获取所有Token
func (tm *TokenManager) GetAllTokens() map[string]*Token {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	result := make(map[string]*Token)
	for k, v := range tm.tokens {
		result[k] = v
	}
	return result
}

// GetUnbindedTokens 获取未绑定的Token
func (tm *TokenManager) GetUnbindedTokens() map[string]*Token {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	result := make(map[string]*Token)
	for k, v := range tm.unbindedTokens {
		result[k] = v
	}
	return result
}

// UpdateTokenUsage 更新Token使用量
func (tm *TokenManager) UpdateTokenUsage(tokenID string, usedDelta int) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	token, exists := tm.tokens[tokenID]
	if !exists {
		return fmt.Errorf("Token不存在: %s", tokenID)
	}

	token.Used += usedDelta
	if token.Used < 0 {
		token.Used = 0
	}

	return nil
}

// UpdateTokenConnNum 更新Token连接数
func (tm *TokenManager) UpdateTokenConnNum(tokenID string, connDelta int) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	token, exists := tm.tokens[tokenID]
	if !exists {
		return fmt.Errorf("Token不存在: %s", tokenID)
	}

	token.ConnNum += connDelta
	if token.ConnNum < 0 {
		token.ConnNum = 0
	}

	log.Printf("Token %s 连接数更新到 %d", tokenID, token.ConnNum)
	return nil
}

// tryBindToken 尝试绑定Token到可用的Receiver
func (tm *TokenManager) tryBindToken(tokenID string) {
	token := tm.tokens[tokenID]
	
	// 将Token放入未绑定列表，等待Receiver管理器处理
	tm.unbindedTokens[tokenID] = token
	log.Printf("Token %s 等待绑定 (能力: %v)", tokenID, token.Capability)

	// 发布绑定请求事件
	bindingData := map[string]interface{}{
		"type":     "token_binding",
		"token_id": tokenID,
		"token":    token,
	}
	event := NewEvent(BindingRequested, bindingData, tm.GetName())
	if err := tm.eventBus.Publish(event); err != nil {
		log.Printf("发布BindingRequested事件失败: %v", err)
	}
}

// handleReceiverCreated 处理Receiver创建事件
func (tm *TokenManager) handleReceiverCreated(event *Event) error {
	// 当有新的Receiver创建时，尝试绑定未绑定的Token
	log.Printf("TokenManager收到ReceiverCreated事件，尝试绑定未绑定的Token")
	
	tm.mu.RLock()
	unbindedTokens := make([]string, 0, len(tm.unbindedTokens))
	for tokenID := range tm.unbindedTokens {
		unbindedTokens = append(unbindedTokens, tokenID)
	}
	tm.mu.RUnlock()

	// 为每个未绑定的Token发布绑定请求
	for _, tokenID := range unbindedTokens {
		tm.mu.RLock()
		token, exists := tm.tokens[tokenID]
		tm.mu.RUnlock()
		
		if !exists {
			continue
		}

		bindingData := map[string]interface{}{
			"type":     "token_binding",
			"token_id": tokenID,
			"token":    token,
		}
		bindEvent := NewEvent(BindingRequested, bindingData, tm.GetName())
		if err := tm.eventBus.Publish(bindEvent); err != nil {
			log.Printf("发布Token绑定请求失败: %v", err)
		}
	}

	return nil
}

// handleReceiverDeleted 处理Receiver删除事件
func (tm *TokenManager) handleReceiverDeleted(event *Event) error {
	// Receiver删除时，相关的Token连接数会由ReceiverManager处理
	// 这里主要是记录日志
	log.Printf("TokenManager收到ReceiverDeleted事件")
	return nil
}

// handleBindingRequested 处理绑定请求事件
func (tm *TokenManager) handleBindingRequested(event *Event) error {
	// 处理来自其他管理器的绑定请求
	data, ok := event.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("绑定请求事件数据格式错误")
	}

	bindingType, ok := data["type"].(string)
	if !ok || bindingType != "receiver_binding" {
		return nil // 不是针对Token的绑定请求
	}

	// 这里可以处理来自ReceiverManager的绑定完成通知
	return nil
}

// MarkTokenBound 标记Token已绑定（从未绑定列表中移除）
func (tm *TokenManager) MarkTokenBound(tokenID string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	delete(tm.unbindedTokens, tokenID)
	log.Printf("Token %s 已绑定，从未绑定列表中移除", tokenID)
}
