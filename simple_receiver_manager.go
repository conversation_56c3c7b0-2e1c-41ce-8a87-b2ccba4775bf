package main

import (
	"fmt"
	"log"
)

// SimpleReceiverManager 简化的Receiver资源管理器
// 移除事件系统和并发控制，保持核心业务逻辑不变
type SimpleReceiverManager struct {
	receivers                map[string]*Receiver
	unbindedReceivers        map[string]*Receiver
	availableReceiversByType map[int][]*Receiver
	enableMixedMode          bool
	tokenManager             *SimpleTokenManager // 引用SimpleTokenManager以便直接操作
	storage                  SimpleStorage       // 可选的存储后端
}

// NewSimpleReceiverManager 创建简化的Receiver管理器
func NewSimpleReceiverManager(tokenManager *SimpleTokenManager) *SimpleReceiverManager {
	return &SimpleReceiverManager{
		receivers:                make(map[string]*Receiver),
		unbindedReceivers:        make(map[string]*Receiver),
		availableReceiversByType: make(map[int][]*Receiver),
		enableMixedMode:          false,
		tokenManager:             tokenManager,
	}
}

// NewSimpleReceiverManagerWithStorage 创建带存储的简化Receiver管理器
func NewSimpleReceiverManagerWithStorage(tokenManager *SimpleTokenManager, storage SimpleStorage) *SimpleReceiverManager {
	return &SimpleReceiverManager{
		receivers:                make(map[string]*Receiver),
		unbindedReceivers:        make(map[string]*Receiver),
		availableReceiversByType: make(map[int][]*Receiver),
		enableMixedMode:          false,
		tokenManager:             tokenManager,
		storage:                  storage,
	}
}

// GetName 获取管理器名称
func (srm *SimpleReceiverManager) GetName() string {
	return "SimpleReceiverManager"
}

// AddNewReceiverWithCapabilities 创建支持自定义ID和能力的Receiver
func (srm *SimpleReceiverManager) AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver {
	// 如果没有提供能力，默认为 stock_a
	if len(capabilities) == 0 {
		capabilities = []string{"stock_a"}
	}

	// 应用特殊规则（复用balance.go中的逻辑）
	capabilities = applySpecialRulesToCapabilities(capabilities)

	receiver := &Receiver{
		ID:             id,
		TokenID:        "", // 初始时未绑定
		Used:           0,
		Capability:     capabilities,
		BindProducts:   make([]*Product, 0),
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}

	srm.receivers[id] = receiver
	log.Printf("创建 Receiver: %s (产品支持类型: %v)", id, capabilities)

	// 如果有存储，保存到存储
	if srm.storage != nil {
		if err := srm.storage.Set(fmt.Sprintf("receiver:%s", id), receiver); err != nil {
			log.Printf("保存Receiver到存储失败: %v", err)
		}
	}

	// 尝试绑定到可用的Token（直接调用）
	srm.tryBindReceiver(id)

	return receiver
}

// RemoveReceiver 删除Receiver
func (srm *SimpleReceiverManager) RemoveReceiver(receiverID string) bool {
	receiver, exists := srm.receivers[receiverID]
	if !exists {
		return false
	}

	log.Printf("删除 Receiver: %s (能力: %v, 绑定Token: %s， 解绑产品: %d 个)",
		receiverID, receiver.Capability, receiver.TokenID, len(receiver.BindProducts))

	// 如果绑定了Token，通知TokenManager处理（直接调用）
	if receiver.TokenID != "" && srm.tokenManager != nil {
		srm.tokenManager.HandleReceiverDeleted(receiver.TokenID)
	}

	// 从可用切片中移除
	srm.removeFromAvailableReceivers(receiver)
	// 从本地存储中删除
	delete(srm.unbindedReceivers, receiverID)
	delete(srm.receivers, receiverID)

	// 如果有存储，从存储中删除
	if srm.storage != nil {
		if err := srm.storage.Delete(fmt.Sprintf("receiver:%s", receiverID)); err != nil {
			log.Printf("从存储删除Receiver失败: %v", err)
		}
	}

	return true
}

// GetReceiver 获取Receiver
func (srm *SimpleReceiverManager) GetReceiver(receiverID string) (*Receiver, bool) {
	receiver, exists := srm.receivers[receiverID]
	return receiver, exists
}

// GetAllReceivers 获取所有Receiver
func (srm *SimpleReceiverManager) GetAllReceivers() map[string]*Receiver {
	result := make(map[string]*Receiver)
	for k, v := range srm.receivers {
		result[k] = v
	}
	return result
}

// GetAvailableReceiversByType 获取按类型分组的可用Receiver
func (srm *SimpleReceiverManager) GetAvailableReceiversByType() map[int][]*Receiver {
	result := make(map[int][]*Receiver)
	for k, v := range srm.availableReceiversByType {
		result[k] = make([]*Receiver, len(v))
		copy(result[k], v)
	}
	return result
}

// UpdateReceiverUsage 更新Receiver使用量
func (srm *SimpleReceiverManager) UpdateReceiverUsage(receiverID string, usedDelta int) error {
	receiver, exists := srm.receivers[receiverID]
	if !exists {
		return fmt.Errorf("Receiver不存在: %s", receiverID)
	}

	oldUsed := receiver.Used
	receiver.Used += usedDelta
	if receiver.Used < 0 {
		receiver.Used = 0
	}

	// 如果使用量变化影响可用性，更新可用切片
	if oldUsed < 500 && receiver.Used >= 500 {
		// 从可用变为满载
		srm.removeFromAvailableReceivers(receiver)
		log.Printf("Receiver %s 已满载，从可用列表中移除", receiverID)
	} else if oldUsed >= 500 && receiver.Used < 500 && receiver.TokenID != "" {
		// 从满载变为可用
		srm.addToAvailableReceivers(receiver)
		log.Printf("Receiver %s 重新可用，加入可用列表", receiverID)
	}

	// 如果有存储，更新存储
	if srm.storage != nil {
		if err := srm.storage.Set(fmt.Sprintf("receiver:%s", receiverID), receiver); err != nil {
			log.Printf("更新Receiver到存储失败: %v", err)
		}
	}

	return nil
}

// SetMixedMode 设置混用模式
func (srm *SimpleReceiverManager) SetMixedMode(enabled bool) {
	if srm.enableMixedMode != enabled {
		srm.enableMixedMode = enabled
		log.Printf("SimpleReceiverManager混用模式设置为: %v", enabled)
	}
}

// addToAvailableReceivers 将receiver添加到对应类型的可用切片中
func (srm *SimpleReceiverManager) addToAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	srm.availableReceiversByType[capType] = append(srm.availableReceiversByType[capType], receiver)
}

// removeFromAvailableReceivers 从对应类型的可用切片中移除receiver
func (srm *SimpleReceiverManager) removeFromAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	receivers := srm.availableReceiversByType[capType]
	for i, r := range receivers {
		if r.ID == receiver.ID {
			// 移除该元素
			srm.availableReceiversByType[capType] = append(receivers[:i], receivers[i+1:]...)
			break
		}
	}
}

// tryBindReceiver 尝试绑定Receiver到可用的Token（简化版本）
func (srm *SimpleReceiverManager) tryBindReceiver(receiverID string) {
	receiver := srm.receivers[receiverID]

	// 如果没有TokenManager，直接放入未绑定列表
	if srm.tokenManager == nil {
		srm.unbindedReceivers[receiverID] = receiver
		log.Printf("Receiver %s 等待绑定 (能力: %v)", receiverID, receiver.Capability)
		return
	}

	// 获取可用于绑定的Token列表
	availableTokens := srm.tokenManager.GetTokensForBinding()

	// 查找匹配的Token并绑定
	for tokenID, token := range availableTokens {
		if isCapabilityCompatible(token.capabilityBits, receiver.capabilityBits, srm.enableMixedMode) && token.ConnNum < 10 {
			// 绑定receiver到token
			receiver.TokenID = tokenID

			// 通过TokenManager更新Token状态（直接调用）
			if srm.tokenManager.TryBindToReceiver(tokenID, receiver.capabilityBits, srm.enableMixedMode) {
				// 将receiver加入到对应类型的可用切片中（如果未满）
				if receiver.Used < 500 {
					srm.addToAvailableReceivers(receiver)
				}

				log.Printf("Receiver %s 自动绑定到 Token %s (Receiver能力: %v, Token能力: %v)",
					receiverID, tokenID, receiver.Capability, token.Capability)
				return // 只绑定一个token
			}
		}
	}

	// 没有找到匹配的token，将receiver放入未绑定列表
	srm.unbindedReceivers[receiverID] = receiver
	log.Printf("Receiver %s 暂时未绑定，等待合适的 Token (能力: %v)", receiverID, receiver.Capability)
}

// HandleTokenCreated 处理Token创建（直接调用方法）
func (srm *SimpleReceiverManager) HandleTokenCreated() {
	log.Printf("SimpleReceiverManager收到Token创建通知，尝试绑定未绑定的Receiver")

	// 为每个未绑定的Receiver尝试绑定
	for receiverID := range srm.unbindedReceivers {
		srm.tryBindReceiver(receiverID)
	}
}

// HandleTokenDeleted 处理Token删除（直接调用方法）
func (srm *SimpleReceiverManager) HandleTokenDeleted(tokenID string) {
	log.Printf("SimpleReceiverManager收到Token删除通知: %s", tokenID)

	// 解绑所有相关的Receiver
	var unboundReceivers []string
	for _, receiver := range srm.receivers {
		if receiver.TokenID == tokenID {
			receiver.TokenID = ""
			unboundReceivers = append(unboundReceivers, receiver.ID)
			// 从可用切片中移除
			srm.removeFromAvailableReceivers(receiver)
			// 放入未绑定列表
			srm.unbindedReceivers[receiver.ID] = receiver
		}
	}

	if len(unboundReceivers) > 0 {
		log.Printf("Token %s 删除后，解绑了 %d 个 Receiver: %v", tokenID, len(unboundReceivers), unboundReceivers)

		// 尝试重新绑定到其他Token
		for _, receiverID := range unboundReceivers {
			srm.tryBindReceiver(receiverID)
		}
	}
}

// LoadFromStorage 从存储加载Receiver数据
func (srm *SimpleReceiverManager) LoadFromStorage() error {
	if srm.storage == nil {
		return nil
	}

	// 列出所有receiver键
	data, err := srm.storage.List("receiver:")
	if err != nil {
		return fmt.Errorf("从存储加载Receiver失败: %v", err)
	}

	for key, value := range data {
		if receiver, ok := value.(*Receiver); ok {
			srm.receivers[receiver.ID] = receiver
			log.Printf("从存储加载Receiver: %s", receiver.ID)
		}
	}

	return nil
}
