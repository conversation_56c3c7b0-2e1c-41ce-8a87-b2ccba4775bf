package main

import (
	"fmt"
	"log"
)

// SimpleProductManager 简化的Product资源管理器
// 移除事件系统和并发控制，保持核心业务逻辑不变
type SimpleProductManager struct {
	products         map[string]*Product
	productsByType   map[int][]*Product
	unbindedProducts map[string]*Product
	enableMixedMode  bool
	receiverManager  *SimpleReceiverManager // 引用SimpleReceiverManager以便获取可用Receiver
	tokenManager     *SimpleTokenManager    // 引用SimpleTokenManager以便更新使用量
	storage          SimpleStorage          // 可选的存储后端
}

// NewSimpleProductManager 创建简化的Product管理器
func NewSimpleProductManager(receiverManager *SimpleReceiverManager, tokenManager *SimpleTokenManager) *SimpleProductManager {
	return &SimpleProductManager{
		products:         make(map[string]*Product),
		productsByType:   make(map[int][]*Product),
		unbindedProducts: make(map[string]*Product),
		enableMixedMode:  false,
		receiverManager:  receiverManager,
		tokenManager:     tokenManager,
	}
}

// NewSimpleProductManagerWithStorage 创建带存储的简化Product管理器
func NewSimpleProductManagerWithStorage(receiverManager *SimpleReceiverManager, tokenManager *SimpleTokenManager, storage SimpleStorage) *SimpleProductManager {
	return &SimpleProductManager{
		products:         make(map[string]*Product),
		productsByType:   make(map[int][]*Product),
		unbindedProducts: make(map[string]*Product),
		enableMixedMode:  false,
		receiverManager:  receiverManager,
		tokenManager:     tokenManager,
		storage:          storage,
	}
}

// GetName 获取管理器名称
func (spm *SimpleProductManager) GetName() string {
	return "SimpleProductManager"
}

// AddProduct 添加产品
func (spm *SimpleProductManager) AddProduct(code, symbol string, productType int) *Product {
	// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值（复用balance.go逻辑）
	if productType >= 3 && productType <= 5 { // stock_us, stock_hk, stock_a
		symbol = code
		log.Printf("股票类型产品 %s，符号自动设置为代码值: %s", code, symbol)
	}

	product := &Product{
		Code:     code,
		Symbol:   symbol,
		Type:     productType,
		Receiver: nil, // 初始时未分配
	}

	spm.products[code] = product
	spm.productsByType[productType] = append(spm.productsByType[productType], product)

	log.Printf("创建产品: %s (类型:%d, 符号:%s)", code, productType, symbol)

	// 如果有存储，保存到存储
	if spm.storage != nil {
		if err := spm.storage.Set(fmt.Sprintf("product:%s", code), product); err != nil {
			log.Printf("保存Product到存储失败: %v", err)
		}
	}

	// 尝试自动绑定到合适的 Receiver（直接调用）
	spm.tryBindProduct(product)

	return product
}

// RemoveProduct 删除产品
func (spm *SimpleProductManager) RemoveProduct(code string) bool {
	product, exists := spm.products[code]
	if !exists {
		return false
	}

	// 如果产品绑定了 Receiver，需要减少使用量（直接调用管理器方法）
	if product.Receiver != nil {
		// 通过ReceiverManager更新使用量
		if spm.receiverManager != nil {
			if err := spm.receiverManager.UpdateReceiverUsage(product.Receiver.ID, -1); err != nil {
				log.Printf("更新Receiver使用量失败: %v", err)
			}
		}

		// 通过TokenManager更新使用量
		if product.Receiver.TokenID != "" && spm.tokenManager != nil {
			if err := spm.tokenManager.UpdateTokenUsage(product.Receiver.TokenID, -1); err != nil {
				log.Printf("更新Token使用量失败: %v", err)
			}
		}

		log.Printf("删除产品 %s，Receiver %s 使用量减少", code, product.Receiver.ID)
	}

	// 从按类型分组的切片中移除
	products := spm.productsByType[product.Type]
	for i, p := range products {
		if p.Code == code {
			spm.productsByType[product.Type] = append(products[:i], products[i+1:]...)
			break
		}
	}

	delete(spm.products, code)
	delete(spm.unbindedProducts, code)
	log.Printf("删除产品: %s (类型:%d)", code, product.Type)

	// 如果有存储，从存储中删除
	if spm.storage != nil {
		if err := spm.storage.Delete(fmt.Sprintf("product:%s", code)); err != nil {
			log.Printf("从存储删除Product失败: %v", err)
		}
	}

	return true
}

// AddProductsBatch 批量添加产品（复用balance.go逻辑）
func (spm *SimpleProductManager) AddProductsBatch(opt *ProductOpt) []*Product {
	var products []*Product

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		var symbol string

		// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值
		if opt.Type >= 3 && opt.Type <= 5 { // stock_us, stock_hk, stock_a
			symbol = code
		} else {
			symbol = code // 默认也使用 code 作为 symbol，可以后续修改
		}

		product := spm.AddProduct(code, symbol, opt.Type)
		products = append(products, product)

		log.Printf("批量创建产品: %s (类型:%d, 符号:%s)", code, opt.Type, symbol)
	}

	log.Printf("批量创建完成，共创建 %d 个产品", len(products))
	return products
}

// RemoveProductsBatch 批量删除产品（复用balance.go逻辑）
func (spm *SimpleProductManager) RemoveProductsBatch(opt *ProductOpt) int {
	removedCount := 0

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		if spm.RemoveProduct(code) {
			removedCount++
			log.Printf("批量删除产品: %s", code)
		}
	}

	log.Printf("批量删除完成，共删除 %d 个产品", removedCount)
	return removedCount
}

// AllocateProduct 分配产品（复用balance.go的两阶段分配算法）
func (spm *SimpleProductManager) AllocateProduct(productType int) (receiverID, tokenID string) {
	if spm.receiverManager == nil {
		log.Printf("ReceiverManager未初始化，无法分配产品")
		return "", ""
	}

	// 获取可用的Receiver
	availableReceiversByType := spm.receiverManager.GetAvailableReceiversByType()

	// 阶段1：尝试完全匹配的receiver（优先级最高）
	availableReceivers := availableReceiversByType[productType]
	if len(availableReceivers) > 0 {
		receiver := availableReceivers[0]

		// 检查receiver和token是否还有容量，并且token支持该产品类型
		targetCapability := productTypeToCapability(productType)
		if receiver.Used < 500 && receiver.TokenID != "" {
			// 获取Token信息
			if spm.tokenManager != nil {
				if token, exists := spm.tokenManager.GetToken(receiver.TokenID); exists {
					if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
						// 分配产品 - 更新使用量
						if err := spm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
							log.Printf("更新Receiver使用量失败: %v", err)
							return "", ""
						}
						if err := spm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
							log.Printf("更新Token使用量失败: %v", err)
							// 回滚Receiver使用量
							spm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
							return "", ""
						}

						log.Printf("产品分配成功: Receiver %s, Token %s (类型:%d)", receiver.ID, receiver.TokenID, productType)
						return receiver.ID, receiver.TokenID
					}
				}
			}
		}
	}

	// 阶段2：如果启用混用模式，尝试兼容类型的receiver
	if spm.enableMixedMode {
		for _, receivers := range availableReceiversByType {
			if len(receivers) > 0 {
				receiver := receivers[0]

				// 检查token是否支持该产品类型
				targetCapability := productTypeToCapability(productType)
				if receiver.Used < 500 && receiver.TokenID != "" {
					if spm.tokenManager != nil {
						if token, exists := spm.tokenManager.GetToken(receiver.TokenID); exists {
							if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
								// 分配产品 - 更新使用量
								if err := spm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
									log.Printf("更新Receiver使用量失败: %v", err)
									continue
								}
								if err := spm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
									log.Printf("更新Token使用量失败: %v", err)
									// 回滚Receiver使用量
									spm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
									continue
								}

								log.Printf("产品分配成功(混用模式): Receiver %s, Token %s (类型:%d)", receiver.ID, receiver.TokenID, productType)
								return receiver.ID, receiver.TokenID
							}
						}
					}
				}
			}
		}
	}

	// 阶段3：没有可用的receiver，分配失败
	log.Printf("产品分配失败: 没有可用的Receiver (类型:%d)", productType)
	return "", ""
}

// GetProduct 获取产品
func (spm *SimpleProductManager) GetProduct(code string) (*Product, bool) {
	product, exists := spm.products[code]
	return product, exists
}

// GetAllProducts 获取所有产品
func (spm *SimpleProductManager) GetAllProducts() map[string]*Product {
	result := make(map[string]*Product)
	for k, v := range spm.products {
		result[k] = v
	}
	return result
}

// GetProductsByType 获取按类型分组的产品
func (spm *SimpleProductManager) GetProductsByType() map[int][]*Product {
	result := make(map[int][]*Product)
	for k, v := range spm.productsByType {
		result[k] = make([]*Product, len(v))
		copy(result[k], v)
	}
	return result
}

// SetMixedMode 设置混用模式
func (spm *SimpleProductManager) SetMixedMode(enabled bool) {
	if spm.enableMixedMode != enabled {
		spm.enableMixedMode = enabled
		log.Printf("SimpleProductManager混用模式设置为: %v", enabled)
	}
}

// LoadFromStorage 从存储加载Product数据
func (spm *SimpleProductManager) LoadFromStorage() error {
	if spm.storage == nil {
		return nil
	}

	// 列出所有product键
	data, err := spm.storage.List("product:")
	if err != nil {
		return fmt.Errorf("从存储加载Product失败: %v", err)
	}

	for key, value := range data {
		if product, ok := value.(*Product); ok {
			spm.products[product.Code] = product
			spm.productsByType[product.Type] = append(spm.productsByType[product.Type], product)
			log.Printf("从存储加载Product: %s", product.Code)
		}
	}

	return nil
}

// tryBindProduct 尝试为产品绑定合适的Receiver（复用balance.go逻辑）
func (spm *SimpleProductManager) tryBindProduct(product *Product) {
	if spm.receiverManager == nil || spm.tokenManager == nil {
		spm.unbindedProducts[product.Code] = product
		log.Printf("产品 %s 暂时未绑定，等待管理器初始化", product.Code)
		return
	}

	targetCapability := productTypeToCapability(product.Type)

	// 查找可用的Receiver
	allReceivers := spm.receiverManager.GetAllReceivers()
	for _, receiver := range allReceivers {
		// 检查Receiver是否有容量且已绑定Token
		if receiver.Used < 500 && receiver.TokenID != "" {
			// 检查Receiver是否支持该能力
			if hasCapability(receiver.capabilityBits, targetCapability) {
				// 检查绑定的Token是否也支持该能力且有容量
				if token, exists := spm.tokenManager.GetToken(receiver.TokenID); exists {
					if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
						// 绑定产品到该Receiver
						product.Receiver = receiver

						// 更新使用量
						if err := spm.receiverManager.UpdateReceiverUsage(receiver.ID, 1); err != nil {
							log.Printf("更新Receiver使用量失败: %v", err)
							product.Receiver = nil
							continue
						}
						if err := spm.tokenManager.UpdateTokenUsage(receiver.TokenID, 1); err != nil {
							log.Printf("更新Token使用量失败: %v", err)
							// 回滚Receiver使用量
							spm.receiverManager.UpdateReceiverUsage(receiver.ID, -1)
							product.Receiver = nil
							continue
						}

						log.Printf("产品 %s (类型:%d, 能力:%s) 自动绑定到 Receiver %s (Token: %s)",
							product.Code, product.Type, targetCapability, receiver.ID, token.ID)

						return
					}
				}
			}
		}
	}

	// 未找到合适的Receiver，放入未绑定列表
	spm.unbindedProducts[product.Code] = product
	log.Printf("产品 %s (类型:%d, 能力:%s) 未找到合适的 Receiver 进行绑定",
		product.Code, product.Type, targetCapability)
}

// TryBindUnboundProducts 尝试为所有未绑定的产品进行绑定（供其他管理器调用）
func (spm *SimpleProductManager) TryBindUnboundProducts() {
	unboundCount := 0
	boundCount := 0

	// 创建未绑定产品的副本以避免并发修改
	unboundProducts := make([]*Product, 0, len(spm.unbindedProducts))
	for _, product := range spm.unbindedProducts {
		unboundProducts = append(unboundProducts, product)
	}

	for _, product := range unboundProducts {
		if product.Receiver == nil {
			unboundCount++
			oldReceiver := product.Receiver
			spm.tryBindProduct(product)
			if product.Receiver != nil && product.Receiver != oldReceiver {
				boundCount++
				// 从未绑定列表中移除
				delete(spm.unbindedProducts, product.Code)
			}
		}
	}

	if unboundCount > 0 {
		log.Printf("尝试绑定未绑定产品: 总计 %d 个，成功绑定 %d 个", unboundCount, boundCount)
	}
}

// HandleReceiverCreated 处理Receiver创建（直接调用方法）
func (spm *SimpleProductManager) HandleReceiverCreated() {
	log.Printf("SimpleProductManager收到Receiver创建通知，尝试绑定未绑定的产品")
	spm.TryBindUnboundProducts()
}

// HandleReceiverDeleted 处理Receiver删除（直接调用方法）
func (spm *SimpleProductManager) HandleReceiverDeleted(receiverID string) {
	log.Printf("SimpleProductManager收到Receiver删除通知: %s", receiverID)

	// 解绑所有相关的产品
	var unboundProducts []string
	for _, product := range spm.products {
		if product.Receiver != nil && product.Receiver.ID == receiverID {
			product.Receiver = nil
			spm.unbindedProducts[product.Code] = product
			unboundProducts = append(unboundProducts, product.Code)
		}
	}

	if len(unboundProducts) > 0 {
		log.Printf("Receiver %s 删除后，解绑了 %d 个产品: %v", receiverID, len(unboundProducts), unboundProducts)

		// 尝试重新绑定到其他Receiver
		spm.TryBindUnboundProducts()
	}
}
