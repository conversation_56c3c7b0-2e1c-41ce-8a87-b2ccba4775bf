package main

import (
	"log"
)

func main() {
	// 创建简化协调器实例
	coordinator := NewSimpleCoordinator()

	// 启动协调器（简化版本，立即返回）
	if err := coordinator.Start(nil); err != nil {
		log.Fatalf("启动简化协调器失败: %v", err)
	}

	log.Println("简化架构系统启动成功")

	// 初始化测试数据（保持与原版本相同）
	log.Println("初始化测试数据...")

	// 创建不同类型的 Token
	_ = coordinator.AddNewTokenWithCapabilities("company", "", "", "", 3000, []string{"stock_us", "stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("utgl", "", "", "", 5000, []string{"stock_us", "stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("erik", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("frewen", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("leo", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("greet", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("ryan", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("alex", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("jade", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("meyer", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("cavill", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("lee", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("ariel", "", "", "", 500, []string{"stock_us"})

	// 创建组合类型的 Token（注释掉，与原版本保持一致）
	// tokenCombo := coordinator.AddNewTokenWithCapabilities("token-5", "", "", "", 1200, []string{"stock_us", "stock_hk", "stock_a"})

	log.Printf("创建了 %d 个 Token", len(coordinator.GetTokens()))

	// 创建不同类型的 Receiver
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-1", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-2", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-3", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-4", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-5", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-6", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-7", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-8", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-9", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-10", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-11", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-12", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-13", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-14", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-15", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-16", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-1", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-2", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-3", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-4", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-5", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-6", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-7", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-1", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-2", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-3", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-4", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-5", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-6", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-7", []string{"stock_hk"})

	log.Printf("创建了 %d 个 Receiver", len(coordinator.GetReceivers()))

	// 简化版本无需等待事件处理（没有异步事件）
	log.Println("测试数据初始化完成（简化版本无异步处理）")

	// 启动 API 服务器
	log.Println("启动 API 服务器在端口 8080...")
	log.Println("使用简化架构 - 更快的启动速度，更低的资源消耗")
	log.Println("")
	log.Println("API 文档:")
	log.Println("  Token 管理:")
	log.Println("    POST   /api/v1/tokens          - 创建 Token")
	log.Println("    GET    /api/v1/tokens          - 获取所有 Token")
	log.Println("    GET    /api/v1/tokens/:id      - 获取指定 Token")
	log.Println("    PUT    /api/v1/tokens/:id      - 更新 Token")
	log.Println("    DELETE /api/v1/tokens/:id      - 删除 Token")
	log.Println("  Receiver 管理:")
	log.Println("    POST   /api/v1/receivers       - 创建 Receiver")
	log.Println("    GET    /api/v1/receivers       - 获取所有 Receiver")
	log.Println("    GET    /api/v1/receivers/:id   - 获取指定 Receiver")
	log.Println("    PUT    /api/v1/receivers/:id   - 更新 Receiver")
	log.Println("    DELETE /api/v1/receivers/:id   - 删除 Receiver")
	log.Println("  Product 管理:")
	log.Println("    POST   /api/v1/products          - 创建产品")
	log.Println("    GET    /api/v1/products          - 获取所有产品")
	log.Println("    GET    /api/v1/products/:code    - 获取指定产品")
	log.Println("    PUT    /api/v1/products/:code    - 更新产品")
	log.Println("    DELETE /api/v1/products/:code    - 删除产品")
	log.Println("    POST   /api/v1/products/allocate - 分配产品")
	log.Println("    POST   /api/v1/products/batch    - 批量创建产品")
	log.Println("    DELETE /api/v1/products/batch    - 批量删除产品")
	log.Println("    GET    /api/v1/products/stats    - 获取统计信息")
	log.Println("  系统配置:")
	log.Println("    GET    /api/v1/system/status           - 获取系统状态")
	log.Println("    GET    /api/v1/system/health           - 获取架构健康状态")
	log.Println("    PUT    /api/v1/system/mixed-mode       - 设置混用模式")
	log.Println("")
	log.Println("简化架构特性:")
	log.Println("  ✓ 移除事件系统复杂性")
	log.Println("  ✓ 移除并发控制开销")
	log.Println("  ✓ 直接方法调用，更快响应")
	log.Println("  ✓ 更低的内存使用")
	log.Println("  ✓ 更简单的调试和维护")
	log.Println("  ✓ 完全兼容原有API接口")
	log.Println("")

	// 简化的API服务器启动（移除复杂的信号处理和优雅关闭）
	log.Println("正在启动API服务器...")
	log.Println("按 Ctrl+C 停止服务器")

	// 直接启动API服务器（阻塞调用）
	StartAPIServer(coordinator, "8080")
}
