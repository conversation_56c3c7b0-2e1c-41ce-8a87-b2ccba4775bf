package main

import "log"

// 已移除：不再使用自动生成的计数器

// 能力类型常量
const (
	CapabilityForex   = 1 << 0 // 1: forex
	CapabilityEnergy  = 1 << 1 // 2: energy
	CapabilityStockUS = 1 << 2 // 4: stock_us
	CapabilityStockHK = 1 << 3 // 8: stock_hk
	CapabilityStockA  = 1 << 4 // 16: stock_a
	CapabilityCrypto  = 1 << 5 // 32: crypto
)

// 已移除：不再使用基于数字类型的映射

// 能力名称映射：字符串 -> 位标志
var capabilityNameMap = map[string]int{
	"forex":    CapabilityForex,
	"energy":   CapabilityEnergy,
	"stock_us": CapabilityStockUS,
	"stock_hk": CapabilityStockHK,
	"stock_a":  CapabilityStockA,
	"crypto":   CapabilityCrypto,
}

// 已移除：不再使用位标志到字符串的映射

type Token struct {
	ID             string   `json:"id" validate:"required"`        // 用户自定义
	Apikey         string   `json:"apikey" validate:"required"`    // 用户自定义
	ApiSecret      string   `json:"apisecret" validate:"required"` // 用户自定义
	ApiToken       string   `json:"apitoken" validate:"required"`  // 用户自定义
	Limit          int      `json:"limit" validate:"default=500"`
	Used           int      `json:"used"`
	ConnNum        int      `json:"conn_num"`
	Capability     []string `json:"capability" validate:"default=stock_a"`
	capabilityBits int      // 内部使用：位运算表示支持的能力
}

type Receiver struct {
	ID             string     `json:"id" validate:"required"`
	TokenID        string     `json:"token_id"`
	Used           int        `json:"used"`
	Capability     []string   `json:"capability" validate:"default=stock_a"`
	BindProducts   []*Product `json:"bind_products"`
	capabilityBits int        // 内部使用：位运算表示支持的能力
}

type Product struct {
	Code     string    `json:"code" validate:"required"`
	Symbol   string    `json:"symbol"`
	Type     int       `json:"type" validate:"min=1,max=6,default=5"`
	Receiver *Receiver `json:"receiver"`
}

type ProductOpt struct {
	Type  int       `json:"type" validate:"required,min=1,max=6"`
	Codes []*string `json:"codes" validate:"required"`
}

type Balancer struct {
	Receivers                map[string]*Receiver
	Tokens                   map[string]*Token
	Products                 map[string]*Product
	ProductsByType           map[int][]*Product
	UnbindedProducts         map[string]*Product
	UnbindedTokens           map[string]*Token    // 未绑定的 token
	UnbindedReceivers        map[string]*Receiver // 未绑定的 receiver
	AvailableReceiversByType map[int][]*Receiver  // 按原始类型分组的可用 receiver 切片
	EnableMixedMode          bool                 // 是否启用混用模式
}

func NewBalancer() *Balancer {
	return &Balancer{
		Receivers:                make(map[string]*Receiver),
		Tokens:                   make(map[string]*Token),
		Products:                 make(map[string]*Product),
		ProductsByType:           make(map[int][]*Product),
		UnbindedProducts:         make(map[string]*Product),
		UnbindedTokens:           make(map[string]*Token),
		UnbindedReceivers:        make(map[string]*Receiver),
		AvailableReceiversByType: make(map[int][]*Receiver),
		EnableMixedMode:          false, // 默认不启用混用模式
	}
}

// 删除 Receiver 时解绑所有相关产品
func (r *Receiver) unBindProducts(b *Balancer) {
	for _, product := range r.BindProducts {
		product.Receiver = nil
		b.UnbindedProducts[product.Code] = product
	}
}

// 已移除：不再使用基于数字类型的能力计算

// 从能力字符串数组计算位标志
func calculateCapabilityBitsFromStrings(capabilities []string) int {
	var bits int
	for _, cap := range capabilities {
		if bitValue, exists := capabilityNameMap[cap]; exists {
			bits |= bitValue
		}
	}
	return bits
}

// 已移除：不再使用位标志生成能力字符串

// 应用特殊规则到能力字符串数组
func applySpecialRulesToCapabilities(capabilities []string) []string {
	capSet := make(map[string]bool)
	for _, cap := range capabilities {
		capSet[cap] = true
	}

	// 特殊规则：stock_us 或 stock_hk 自动包含 stock_a
	if capSet["stock_us"] || capSet["stock_hk"] {
		capSet["stock_a"] = true
	}

	var result []string
	for cap := range capSet {
		result = append(result, cap)
	}
	return result
}

// 已移除：不再使用基于字符串的能力检查

// 检查是否支持指定能力（基于位标志，内部使用）
func hasCapability(capabilityBits int, targetCapability string) bool {
	if targetBits, exists := capabilityNameMap[targetCapability]; exists {
		return (capabilityBits & targetBits) != 0
	}
	return false
}

// 将产品类型数字转换为能力字符串
func productTypeToCapability(productType int) string {
	switch productType {
	case 1:
		return "forex"
	case 2:
		return "energy"
	case 3:
		return "stock_us"
	case 4:
		return "stock_hk"
	case 5:
		return "stock_a"
	case 6:
		return "crypto"
	default:
		return "stock_a" // 默认
	}
}

// 检查两个能力是否兼容（完全匹配或混用模式下的兼容）
func isCapabilityCompatible(tokenBits, receiverBits int, enableMixed bool) bool {
	if enableMixed {
		// 混用模式：只要有任何重叠的能力就兼容
		return (tokenBits & receiverBits) != 0
	} else {
		// 严格模式：必须完全匹配
		return tokenBits == receiverBits
	}
}

// 已移除：不再支持基于 originalType 的 Token 创建

// 已移除：不再支持基于 types 数组的 Token 创建

// 已移除：不再支持基于 types 数组的自定义 Token 创建

// 已移除：不再支持基于 originalType 的自定义 Receiver 创建

// 创建支持自定义 ID 和能力的 Token（直接使用 Capability 字符串数组）
func (b *Balancer) AddNewTokenWithCapabilities(id, apikey, apisecret, apitoken string, limit int, capabilities []string) *Token {
	// 应用特殊规则
	capabilities = applySpecialRulesToCapabilities(capabilities)

	token := &Token{
		ID:             id,
		Apikey:         apikey,
		ApiSecret:      apisecret,
		ApiToken:       apitoken,
		Limit:          limit,
		Used:           0,
		Capability:     capabilities,
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}
	b.Tokens[id] = token
	log.Printf("创建 Token: %s (产品支持类型: %v, 限额: %d)", id, capabilities, limit)
	b.tryBindToken(id)

	// 尝试为未绑定的产品进行绑定
	b.tryBindUnboundProducts()

	return token
}

// 创建支持自定义 ID 和能力的 Receiver（直接使用 Capability 字符串数组）
func (b *Balancer) AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver {
	// 如果没有提供能力，默认为 stock_a
	if len(capabilities) == 0 {
		capabilities = []string{"stock_a"}
	}

	// 应用特殊规则
	capabilities = applySpecialRulesToCapabilities(capabilities)

	receiver := &Receiver{
		ID:             id,
		TokenID:        "", // 初始时未绑定
		Used:           0,
		Capability:     capabilities,
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}
	b.Receivers[id] = receiver
	log.Printf("创建 Receiver: %s (产品支持类型: %v)", id, capabilities)
	b.tryBindReceiver(id)

	// 尝试为未绑定的产品进行绑定
	b.tryBindUnboundProducts()

	return receiver
}

// 删除 Token 时自动解绑相关 Receiver
func (b *Balancer) RemoveToken(tokenID string) bool {
	token, exists := b.Tokens[tokenID]
	if !exists {
		return false
	}

	log.Printf("删除 Token: %s (能力: %v)", tokenID, token.Capability)

	// 解绑所有相关的 Receiver
	var unboundReceivers []string
	for _, receiver := range b.Receivers {
		if receiver.TokenID == tokenID {
			receiver.TokenID = ""
			unboundReceivers = append(unboundReceivers, receiver.ID)
			// 从可用切片中移除
			b.removeFromAvailableReceivers(receiver)
			// 尝试重新绑定到其他 Token
			b.tryBindReceiver(receiver.ID)
		}
	}

	if len(unboundReceivers) > 0 {
		log.Printf("Token %s 删除后，解绑了 %d 个 Receiver: %v", tokenID, len(unboundReceivers), unboundReceivers)
	}

	// 从未绑定列表中移除
	delete(b.UnbindedTokens, tokenID)
	delete(b.Tokens, tokenID)
	return true
}

// 删除 Receiver 时自动更新 Token 连接数
func (b *Balancer) RemoveReceiver(receiverID string) bool {
	receiver, exists := b.Receivers[receiverID]
	if !exists {
		return false
	}

	log.Printf("删除 Receiver: %s (能力: %v, 绑定Token: %s， 解绑产品: %d 个)", receiverID, receiver.Capability, receiver.TokenID, len(receiver.BindProducts))

	// 解绑所有相关的产品
	receiver.unBindProducts(b)

	// 如果绑定了 Token，更新 Token 的连接数
	if receiver.TokenID != "" {
		if token, exists := b.Tokens[receiver.TokenID]; exists {
			token.ConnNum--
			log.Printf("Token %s 连接数减少到 %d", receiver.TokenID, token.ConnNum)
		}
	}

	// 从可用切片中移除
	b.removeFromAvailableReceivers(receiver)
	// 从未绑定列表中移除
	delete(b.UnbindedReceivers, receiverID)
	delete(b.Receivers, receiverID)

	// 尝试为未绑定的产品进行绑定
	b.tryBindUnboundProducts()
	return true
}

/*
增加Token逻辑：
1. 增加Token时，先判断是否有待绑定的receiver,如果没有，放入未绑定的map中，返回
2. 如果有并且能力兼容，直接绑定；将receiver从待绑定map中删除，然后将receiver加入到对应类型的可用切片中
3. 如果能力不兼容，放入未绑定的map中
*/
func (b *Balancer) tryBindToken(tokenID string) {
	curToken := b.Tokens[tokenID]
	if len(b.UnbindedReceivers) == 0 {
		b.UnbindedTokens[tokenID] = curToken
		log.Printf("Token %s 暂时未绑定，等待合适的 Receiver (能力: %v)", tokenID, curToken.Capability)
		return
	}

	// 查找匹配的 receiver 并绑定
	for receiverID, receiver := range b.UnbindedReceivers {
		if isCapabilityCompatible(curToken.capabilityBits, receiver.capabilityBits, b.EnableMixedMode) {
			// 绑定 receiver 到 token
			receiver.TokenID = tokenID
			curToken.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if receiver.Used < 500 {
				b.addToAvailableReceivers(receiver)
			}

			// 从未绑定列表中移除
			delete(b.UnbindedReceivers, receiverID)
			log.Printf("Token %s 自动绑定到 Receiver %s (Token能力: %v, Receiver能力: %v)",
				tokenID, receiverID, curToken.Capability, receiver.Capability)
			return // 只绑定一个 receiver
		}
	}

	// 没有找到匹配的 receiver，将 token 放入未绑定列表
	b.UnbindedTokens[tokenID] = curToken
	log.Printf("Token %s 暂时未绑定，等待合适的 Receiver (能力: %v)", tokenID, curToken.Capability)
}

// 将 receiver 添加到对应类型的可用切片中
func (b *Balancer) addToAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	b.AvailableReceiversByType[capType] = append(b.AvailableReceiversByType[capType], receiver)
}

// 从对应类型的可用切片中移除 receiver
func (b *Balancer) removeFromAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	receivers := b.AvailableReceiversByType[capType]
	for i, r := range receivers {
		if r.ID == receiver.ID {
			// 移除该元素
			b.AvailableReceiversByType[capType] = append(receivers[:i], receivers[i+1:]...)
			break
		}
	}
}

/*
增加Receiver逻辑：
1. 增加Receiver时，先判断是否有未使用并且能力兼容的token
2. 如果可以绑定，直接绑定，并将receiver加入到对应类型的可用切片中
3. 如果没有匹配的token，将receiver放入未绑定的map中
*/
func (b *Balancer) tryBindReceiver(receiverID string) {
	curReceiver := b.Receivers[receiverID]

	// 查找匹配的未绑定 token
	for tokenID, token := range b.UnbindedTokens {
		if isCapabilityCompatible(token.capabilityBits, curReceiver.capabilityBits, b.EnableMixedMode) && token.ConnNum < 10 {
			// 绑定 receiver 到 token
			curReceiver.TokenID = tokenID
			token.ConnNum++

			// 将 receiver 加入到对应类型的可用切片中（如果未满）
			if curReceiver.Used < 500 {
				b.addToAvailableReceivers(curReceiver)
			}

			// 从未绑定列表中移除 token
			delete(b.UnbindedTokens, tokenID)
			log.Printf("Receiver %s 自动绑定到 Token %s (Receiver能力: %v, Token能力: %v)",
				receiverID, tokenID, curReceiver.Capability, token.Capability)
			return // 只绑定一个 token
		}
	}

	// 没有找到匹配的 token，将 receiver 放入未绑定列表
	b.UnbindedReceivers[receiverID] = curReceiver
	log.Printf("Receiver %s 暂时未绑定，等待合适的 Token (能力: %v)", receiverID, curReceiver.Capability)
}

// 已移除：不再支持基于 originalType 的 Receiver 创建

/*
增加产品分配逻辑：
1. 优先从对应类型的可用Receiver切片中取出第一个接收器进行绑定
2. 如果启用混用模式且没有完全匹配的receiver，尝试兼容类型的receiver
3. 绑定Receiver后，更新Receiver和Token已分配产品的数量
4. 如果receiver满了（Used >= 500），从可用切片中移除
5. 如果找不到可用的receiver，创建新的receiver和token
*/
func (b *Balancer) AllocateProduct(productType int) (receiverID, tokenID string) {
	// 阶段1：尝试完全匹配的receiver（优先级最高）
	availableReceivers := b.AvailableReceiversByType[productType]
	if len(availableReceivers) > 0 {
		receiver := availableReceivers[0]
		token := b.Tokens[receiver.TokenID]

		// 检查receiver和token是否还有容量，并且token支持该产品类型
		targetCapability := productTypeToCapability(productType)
		if receiver.Used < 500 && token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
			// 分配产品
			receiver.Used++
			token.Used++

			// 如果receiver满了，从可用切片中移除
			if receiver.Used >= 500 {
				b.removeFromAvailableReceivers(receiver)
			}

			return receiver.ID, receiver.TokenID
		} else {
			// receiver或token已满或不支持该产品类型，从可用切片中移除
			b.removeFromAvailableReceivers(receiver)
			// 递归调用，尝试下一个receiver
			return b.AllocateProduct(productType)
		}
	}

	// 阶段2：如果启用混用模式，尝试兼容类型的receiver
	if b.EnableMixedMode {
		for _, receivers := range b.AvailableReceiversByType {
			if len(receivers) > 0 {
				receiver := receivers[0]
				token := b.Tokens[receiver.TokenID]

				// 检查token是否支持该产品类型
				targetCapability := productTypeToCapability(productType)
				if receiver.Used < 500 && token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
					// 分配产品
					receiver.Used++
					token.Used++

					// 如果receiver满了，从可用切片中移除
					if receiver.Used >= 500 {
						b.removeFromAvailableReceivers(receiver)
					}

					return receiver.ID, receiver.TokenID
				}
			}
		}
	}

	// 阶段3：没有可用的receiver，分配失败
	return "", ""
}

// Product 管理函数

// 添加产品
func (b *Balancer) AddProduct(code, symbol string, productType int) *Product {
	// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值
	if productType >= 3 && productType <= 5 { // stock_us, stock_hk, stock_a
		symbol = code
		log.Printf("股票类型产品 %s，符号自动设置为代码值: %s", code, symbol)
	}

	product := &Product{
		Code:     code,
		Symbol:   symbol,
		Type:     productType,
		Receiver: nil, // 初始时未分配
	}

	b.Products[code] = product
	b.ProductsByType[productType] = append(b.ProductsByType[productType], product)

	// 尝试自动绑定到合适的 Receiver
	b.tryBindProduct(product)

	return product
}

// 批量添加产品（使用 ProductOpt）
func (b *Balancer) AddProductsBatch(opt *ProductOpt) []*Product {
	var products []*Product

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		var symbol string

		// 当产品属于 stock 前缀类型时，symbol 自动取值为 code 的值
		if opt.Type >= 3 && opt.Type <= 5 { // stock_us, stock_hk, stock_a
			symbol = code
		} else {
			symbol = code // 默认也使用 code 作为 symbol，可以后续修改
		}

		product := b.AddProduct(code, symbol, opt.Type)
		products = append(products, product)

		log.Printf("批量创建产品: %s (类型:%d, 符号:%s)", code, opt.Type, symbol)
	}

	log.Printf("批量创建完成，共创建 %d 个产品", len(products))
	return products
}

// 批量删除产品（使用 ProductOpt）
func (b *Balancer) RemoveProductsBatch(opt *ProductOpt) int {
	removedCount := 0

	for _, codePtr := range opt.Codes {
		if codePtr == nil {
			continue
		}

		code := *codePtr
		if b.RemoveProduct(code) {
			removedCount++
			log.Printf("批量删除产品: %s", code)
		}
	}

	log.Printf("批量删除完成，共删除 %d 个产品", removedCount)
	return removedCount
}

// 尝试为产品绑定合适的 Receiver
func (b *Balancer) tryBindProduct(product *Product) {
	targetCapability := productTypeToCapability(product.Type)

	// 查找可用的 Receiver
	for _, receiver := range b.Receivers {
		// 检查 Receiver 是否有容量且已绑定 Token
		if receiver.Used < 500 && receiver.TokenID != "" {
			// 检查 Receiver 是否支持该能力
			if hasCapability(receiver.capabilityBits, targetCapability) {
				// 检查绑定的 Token 是否也支持该能力且有容量
				if token, exists := b.Tokens[receiver.TokenID]; exists {
					if token.Used < token.Limit && hasCapability(token.capabilityBits, targetCapability) {
						// 绑定产品到该 Receiver
						product.Receiver = receiver
						receiver.Used++
						token.Used++

						// 如果 Receiver 满了，从可用列表中移除
						if receiver.Used >= 500 {
							b.removeFromAvailableReceivers(receiver)
						}

						log.Printf("产品 %s (类型:%d, 能力:%s) 自动绑定到 Receiver %s (Token: %s)",
							product.Code, product.Type, targetCapability, receiver.ID, token.ID)
						return
					}
				}
			}
		}
	}

	log.Printf("产品 %s (类型:%d, 能力:%s) 未找到合适的 Receiver 进行绑定",
		product.Code, product.Type, targetCapability)
}

// 尝试为所有未绑定的产品进行绑定
func (b *Balancer) tryBindUnboundProducts() {
	unboundCount := 0
	boundCount := 0

	for _, product := range b.Products {
		if product.Receiver == nil {
			unboundCount++
			oldReceiver := product.Receiver
			b.tryBindProduct(product)
			if product.Receiver != nil && product.Receiver != oldReceiver {
				boundCount++
			}
		}
	}

	if unboundCount > 0 {
		log.Printf("尝试绑定未绑定产品: 总计 %d 个，成功绑定 %d 个", unboundCount, boundCount)
	}
}

// 删除产品
func (b *Balancer) RemoveProduct(code string) bool {
	product, exists := b.Products[code]
	if !exists {
		return false
	}

	// 如果产品绑定了 Receiver，需要减少使用量
	if product.Receiver != nil {
		product.Receiver.Used--
		// 如果绑定的 Token 存在，也减少使用量
		if product.Receiver.TokenID != "" {
			if token, exists := b.Tokens[product.Receiver.TokenID]; exists {
				token.Used--
			}
		}
		log.Printf("删除产品 %s，Receiver %s 使用量减少到 %d",
			code, product.Receiver.ID, product.Receiver.Used)
	}

	// 从按类型分组的切片中移除
	products := b.ProductsByType[product.Type]
	for i, p := range products {
		if p.Code == code {
			b.ProductsByType[product.Type] = append(products[:i], products[i+1:]...)
			break
		}
	}

	delete(b.Products, code)
	delete(b.UnbindedProducts, code)
	log.Printf("删除产品: %s (类型:%d)", code, product.Type)

	return true
}

// 设置混用模式
func (b *Balancer) SetMixedMode(enabled bool) {
	b.EnableMixedMode = enabled
}

// 已移除：不再支持基于 originalType 的能力信息获取

// 从能力字符串数组获取能力信息
func (b *Balancer) GetCapabilityInfoFromStrings(capabilities []string) (bits int, description string) {
	bits = calculateCapabilityBitsFromStrings(capabilities)
	description = getCapabilityDescription(bits)
	return bits, description
}

// 根据能力位标志生成描述
func getCapabilityDescription(bits int) string {
	var capabilities []string
	if bits&CapabilityForex != 0 {
		capabilities = append(capabilities, "forex")
	}
	if bits&CapabilityEnergy != 0 {
		capabilities = append(capabilities, "energy")
	}
	if bits&CapabilityStockUS != 0 {
		capabilities = append(capabilities, "stock_us")
	}
	if bits&CapabilityStockHK != 0 {
		capabilities = append(capabilities, "stock_hk")
	}
	if bits&CapabilityStockA != 0 {
		capabilities = append(capabilities, "stock_a")
	}
	if bits&CapabilityCrypto != 0 {
		capabilities = append(capabilities, "crypto")
	}

	description := ""
	for i, cap := range capabilities {
		if i > 0 {
			description += ", "
		}
		description += cap
	}

	return description
}
