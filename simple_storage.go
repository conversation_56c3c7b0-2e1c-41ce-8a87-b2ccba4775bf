package main

import (
	"encoding/json"
	"fmt"
	"strings"
)

// SimpleStorage 简化的存储接口，移除复杂的Watch机制和异步操作
type SimpleStorage interface {
	// Get 获取单个键值
	Get(key string) (interface{}, error)
	
	// Set 设置键值
	Set(key string, value interface{}) error
	
	// Delete 删除键值
	Delete(key string) error
	
	// List 列出指定前缀的所有键值对
	List(prefix string) (map[string]interface{}, error)
	
	// IsConnected 检查连接状态
	IsConnected() bool
	
	// Close 关闭存储连接
	Close() error
}

// SimpleStorageConfig 简化的存储配置
type SimpleStorageConfig struct {
	Type      string   `json:"type"`      // "memory" 或 "etcd"
	Endpoints []string `json:"endpoints,omitempty"` // etcd endpoints
}

// SimpleMemoryStorage 简化的内存存储实现
type SimpleMemoryStorage struct {
	data      map[string]interface{}
	connected bool
}

// NewSimpleMemoryStorage 创建简化的内存存储
func NewSimpleMemoryStorage() *SimpleMemoryStorage {
	return &SimpleMemoryStorage{
		data:      make(map[string]interface{}),
		connected: true, // 内存存储始终连接
	}
}

// Get 获取单个键值
func (sms *SimpleMemoryStorage) Get(key string) (interface{}, error) {
	if !sms.connected {
		return nil, fmt.Errorf("存储未连接")
	}
	
	value, exists := sms.data[key]
	if !exists {
		return nil, fmt.Errorf("键不存在: %s", key)
	}
	
	return value, nil
}

// Set 设置键值
func (sms *SimpleMemoryStorage) Set(key string, value interface{}) error {
	if !sms.connected {
		return fmt.Errorf("存储未连接")
	}
	
	sms.data[key] = value
	return nil
}

// Delete 删除键值
func (sms *SimpleMemoryStorage) Delete(key string) error {
	if !sms.connected {
		return fmt.Errorf("存储未连接")
	}
	
	_, exists := sms.data[key]
	if !exists {
		return fmt.Errorf("键不存在: %s", key)
	}
	
	delete(sms.data, key)
	return nil
}

// List 列出指定前缀的所有键值对
func (sms *SimpleMemoryStorage) List(prefix string) (map[string]interface{}, error) {
	if !sms.connected {
		return nil, fmt.Errorf("存储未连接")
	}
	
	result := make(map[string]interface{})
	for key, value := range sms.data {
		if strings.HasPrefix(key, prefix) {
			result[key] = value
		}
	}
	
	return result, nil
}

// IsConnected 检查连接状态
func (sms *SimpleMemoryStorage) IsConnected() bool {
	return sms.connected
}

// Close 关闭存储连接
func (sms *SimpleMemoryStorage) Close() error {
	sms.connected = false
	sms.data = make(map[string]interface{}) // 清空数据
	return nil
}

// ===== 数据序列化和反序列化（保留） =====

// SimpleSerializeData 序列化数据
func SimpleSerializeData(data interface{}) ([]byte, error) {
	return json.Marshal(data)
}

// SimpleDeserializeData 反序列化数据
func SimpleDeserializeData(data []byte, target interface{}) error {
	return json.Unmarshal(data, target)
}

// ===== 简化的etcd存储接口定义（预留） =====

// SimpleEtcdStorage 简化的etcd存储实现（预留接口）
type SimpleEtcdStorage struct {
	endpoints []string
	client    interface{} // 预留etcd client
	connected bool
}

// NewSimpleEtcdStorage 创建简化的etcd存储（预留）
func NewSimpleEtcdStorage(endpoints []string) *SimpleEtcdStorage {
	return &SimpleEtcdStorage{
		endpoints: endpoints,
		connected: false,
	}
}

// Get 从etcd获取数据（预留实现）
func (ses *SimpleEtcdStorage) Get(key string) (interface{}, error) {
	// TODO: 实现简化的etcd Get操作
	// 1. 使用etcd client获取数据
	// 2. 反序列化数据
	// 3. 返回结果
	return nil, fmt.Errorf("简化etcd存储暂未实现")
}

// Set 向etcd设置数据（预留实现）
func (ses *SimpleEtcdStorage) Set(key string, value interface{}) error {
	// TODO: 实现简化的etcd Put操作
	// 1. 序列化数据
	// 2. 使用etcd client存储数据
	// 3. 返回结果
	return fmt.Errorf("简化etcd存储暂未实现")
}

// Delete 从etcd删除数据（预留实现）
func (ses *SimpleEtcdStorage) Delete(key string) error {
	// TODO: 实现简化的etcd Delete操作
	return fmt.Errorf("简化etcd存储暂未实现")
}

// List 从etcd列出数据（预留实现）
func (ses *SimpleEtcdStorage) List(prefix string) (map[string]interface{}, error) {
	// TODO: 实现简化的etcd Get with prefix操作
	return nil, fmt.Errorf("简化etcd存储暂未实现")
}

// IsConnected 检查etcd连接状态（预留实现）
func (ses *SimpleEtcdStorage) IsConnected() bool {
	return ses.connected
}

// Close 关闭etcd连接（预留实现）
func (ses *SimpleEtcdStorage) Close() error {
	// TODO: 实现etcd连接关闭
	ses.connected = false
	return nil
}

// ===== 简化的存储工厂 =====

// CreateSimpleStorage 根据配置创建简化的存储实例
func CreateSimpleStorage(config *SimpleStorageConfig) (SimpleStorage, error) {
	switch config.Type {
	case "memory":
		return NewSimpleMemoryStorage(), nil
	case "etcd":
		if len(config.Endpoints) == 0 {
			return nil, fmt.Errorf("etcd存储需要指定endpoints")
		}
		return NewSimpleEtcdStorage(config.Endpoints), nil
	default:
		return nil, fmt.Errorf("不支持的存储类型: %s", config.Type)
	}
}
