package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"
)

// WatchEventType Watch事件类型
type WatchEventType string

const (
	WatchEventPut    WatchEventType = "PUT"
	WatchEventDelete WatchEventType = "DELETE"
)

// WatchEvent Watch事件结构体
type WatchEvent struct {
	Type      WatchEventType `json:"type"`
	Key       string         `json:"key"`
	Value     interface{}    `json:"value"`
	PrevValue interface{}    `json:"prev_value,omitempty"`
	Timestamp time.Time      `json:"timestamp"`
}

// WatchCallback Watch回调函数类型
type WatchCallback func(event *WatchEvent) error

// Storage 存储接口，支持内存存储和etcd存储
type Storage interface {
	// Get 获取单个键值
	Get(ctx context.Context, key string) (interface{}, error)
	
	// Set 设置键值
	Set(ctx context.Context, key string, value interface{}) error
	
	// Delete 删除键值
	Delete(ctx context.Context, key string) error
	
	// List 列出指定前缀的所有键值对
	List(ctx context.Context, prefix string) (map[string]interface{}, error)
	
	// Watch 监听指定前缀的变更
	Watch(ctx context.Context, prefix string, callback WatchCallback) error
	
	// Close 关闭存储连接
	Close() error
	
	// IsConnected 检查连接状态
	IsConnected() bool
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type     string            `json:"type"`     // "memory" 或 "etcd"
	Endpoints []string         `json:"endpoints,omitempty"` // etcd endpoints
	Options  map[string]string `json:"options,omitempty"`   // 其他配置选项
}

// MemoryStorage 内存存储实现
type MemoryStorage struct {
	data      map[string]interface{}
	watchers  map[string][]WatchCallback
	mu        sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
	connected bool
}

// NewMemoryStorage 创建内存存储
func NewMemoryStorage() *MemoryStorage {
	return &MemoryStorage{
		data:      make(map[string]interface{}),
		watchers:  make(map[string][]WatchCallback),
		connected: false,
	}
}

// Connect 连接存储（内存存储无需实际连接）
func (ms *MemoryStorage) Connect(ctx context.Context) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if ms.connected {
		return nil
	}
	
	ms.ctx, ms.cancel = context.WithCancel(ctx)
	ms.connected = true
	
	log.Printf("MemoryStorage连接成功")
	return nil
}

// Get 获取单个键值
func (ms *MemoryStorage) Get(ctx context.Context, key string) (interface{}, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	if !ms.connected {
		return nil, fmt.Errorf("存储未连接")
	}
	
	value, exists := ms.data[key]
	if !exists {
		return nil, fmt.Errorf("键不存在: %s", key)
	}
	
	return value, nil
}

// Set 设置键值
func (ms *MemoryStorage) Set(ctx context.Context, key string, value interface{}) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if !ms.connected {
		return fmt.Errorf("存储未连接")
	}
	
	prevValue := ms.data[key]
	ms.data[key] = value
	
	// 触发Watch回调
	go ms.triggerWatchers(key, WatchEventPut, value, prevValue)
	
	return nil
}

// Delete 删除键值
func (ms *MemoryStorage) Delete(ctx context.Context, key string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if !ms.connected {
		return fmt.Errorf("存储未连接")
	}
	
	prevValue, exists := ms.data[key]
	if !exists {
		return fmt.Errorf("键不存在: %s", key)
	}
	
	delete(ms.data, key)
	
	// 触发Watch回调
	go ms.triggerWatchers(key, WatchEventDelete, nil, prevValue)
	
	return nil
}

// List 列出指定前缀的所有键值对
func (ms *MemoryStorage) List(ctx context.Context, prefix string) (map[string]interface{}, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	if !ms.connected {
		return nil, fmt.Errorf("存储未连接")
	}
	
	result := make(map[string]interface{})
	for key, value := range ms.data {
		if strings.HasPrefix(key, prefix) {
			result[key] = value
		}
	}
	
	return result, nil
}

// Watch 监听指定前缀的变更
func (ms *MemoryStorage) Watch(ctx context.Context, prefix string, callback WatchCallback) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if !ms.connected {
		return fmt.Errorf("存储未连接")
	}
	
	ms.watchers[prefix] = append(ms.watchers[prefix], callback)
	log.Printf("MemoryStorage添加Watch监听: %s", prefix)
	
	return nil
}

// Close 关闭存储连接
func (ms *MemoryStorage) Close() error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	if !ms.connected {
		return nil
	}
	
	ms.connected = false
	if ms.cancel != nil {
		ms.cancel()
	}
	
	// 清空watchers
	ms.watchers = make(map[string][]WatchCallback)
	
	log.Printf("MemoryStorage连接关闭")
	return nil
}

// IsConnected 检查连接状态
func (ms *MemoryStorage) IsConnected() bool {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	return ms.connected
}

// triggerWatchers 触发Watch回调
func (ms *MemoryStorage) triggerWatchers(key string, eventType WatchEventType, value, prevValue interface{}) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	
	event := &WatchEvent{
		Type:      eventType,
		Key:       key,
		Value:     value,
		PrevValue: prevValue,
		Timestamp: time.Now(),
	}
	
	// 查找匹配的watchers
	for prefix, callbacks := range ms.watchers {
		if strings.HasPrefix(key, prefix) {
			for _, callback := range callbacks {
				go func(cb WatchCallback) {
					if err := cb(event); err != nil {
						log.Printf("Watch回调执行失败: %v", err)
					}
				}(callback)
			}
		}
	}
}

// ===== 数据序列化和反序列化 =====

// SerializeData 序列化数据
func SerializeData(data interface{}) ([]byte, error) {
	return json.Marshal(data)
}

// DeserializeData 反序列化数据
func DeserializeData(data []byte, target interface{}) error {
	return json.Unmarshal(data, target)
}

// ===== etcd存储接口定义（预留） =====

// EtcdStorage etcd存储实现（预留接口）
type EtcdStorage struct {
	endpoints []string
	client    interface{} // 预留etcd client
	connected bool
	mu        sync.RWMutex
}

// NewEtcdStorage 创建etcd存储（预留）
func NewEtcdStorage(endpoints []string) *EtcdStorage {
	return &EtcdStorage{
		endpoints: endpoints,
		connected: false,
	}
}

// Connect 连接etcd（预留实现）
func (es *EtcdStorage) Connect(ctx context.Context) error {
	// TODO: 实现etcd连接逻辑
	// 1. 创建etcd client
	// 2. 测试连接
	// 3. 设置连接状态
	return fmt.Errorf("etcd存储暂未实现")
}

// Get 从etcd获取数据（预留实现）
func (es *EtcdStorage) Get(ctx context.Context, key string) (interface{}, error) {
	// TODO: 实现etcd Get操作
	return nil, fmt.Errorf("etcd存储暂未实现")
}

// Set 向etcd设置数据（预留实现）
func (es *EtcdStorage) Set(ctx context.Context, key string, value interface{}) error {
	// TODO: 实现etcd Put操作
	return fmt.Errorf("etcd存储暂未实现")
}

// Delete 从etcd删除数据（预留实现）
func (es *EtcdStorage) Delete(ctx context.Context, key string) error {
	// TODO: 实现etcd Delete操作
	return fmt.Errorf("etcd存储暂未实现")
}

// List 从etcd列出数据（预留实现）
func (es *EtcdStorage) List(ctx context.Context, prefix string) (map[string]interface{}, error) {
	// TODO: 实现etcd Get with prefix操作
	return nil, fmt.Errorf("etcd存储暂未实现")
}

// Watch 监听etcd变更（预留实现）
func (es *EtcdStorage) Watch(ctx context.Context, prefix string, callback WatchCallback) error {
	// TODO: 实现etcd Watch操作
	return fmt.Errorf("etcd存储暂未实现")
}

// Close 关闭etcd连接（预留实现）
func (es *EtcdStorage) Close() error {
	// TODO: 实现etcd连接关闭
	return fmt.Errorf("etcd存储暂未实现")
}

// IsConnected 检查etcd连接状态（预留实现）
func (es *EtcdStorage) IsConnected() bool {
	es.mu.RLock()
	defer es.mu.RUnlock()
	return es.connected
}

// ===== 存储工厂 =====

// CreateStorage 根据配置创建存储实例
func CreateStorage(config *StorageConfig) (Storage, error) {
	switch config.Type {
	case "memory":
		return NewMemoryStorage(), nil
	case "etcd":
		if len(config.Endpoints) == 0 {
			return nil, fmt.Errorf("etcd存储需要指定endpoints")
		}
		return NewEtcdStorage(config.Endpoints), nil
	default:
		return nil, fmt.Errorf("不支持的存储类型: %s", config.Type)
	}
}
