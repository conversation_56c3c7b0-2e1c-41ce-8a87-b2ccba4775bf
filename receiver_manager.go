package main

import (
	"context"
	"fmt"
	"log"
	"sync"
)

// ReceiverData 事件数据结构
type ReceiverData struct {
	Receiver        *Receiver `json:"receiver"`
	BoundTokenID    string    `json:"bound_token_id,omitempty"`
	UnboundProducts []string  `json:"unbound_products,omitempty"`
	EnableMixedMode bool      `json:"enable_mixed_mode,omitempty"`
}

// ReceiverManager Receiver资源管理器
type ReceiverManager struct {
	receivers                map[string]*Receiver
	unbindedReceivers        map[string]*Receiver
	availableReceiversByType map[int][]*Receiver
	enableMixedMode          bool
	eventBus                 EventBus
	tokenManager             *TokenManager // 引用TokenManager以便直接操作
	mu                       sync.RWMutex
	ctx                      context.Context
	cancel                   context.CancelFunc
	running                  bool
}

// NewReceiverManager 创建Receiver管理器
func NewReceiverManager(eventBus EventBus, tokenManager *TokenManager) *ReceiverManager {
	return &ReceiverManager{
		receivers:                make(map[string]*Receiver),
		unbindedReceivers:        make(map[string]*Receiver),
		availableReceiversByType: make(map[int][]*Receiver),
		enableMixedMode:          false,
		eventBus:                 eventBus,
		tokenManager:             tokenManager,
	}
}

// GetName 获取管理器名称
func (rm *ReceiverManager) GetName() string {
	return "ReceiverManager"
}

// Start 启动Receiver管理器
func (rm *ReceiverManager) Start(ctx context.Context) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if rm.running {
		return fmt.Errorf("ReceiverManager已在运行")
	}

	rm.ctx, rm.cancel = context.WithCancel(ctx)
	rm.running = true

	// 订阅相关事件
	if err := rm.eventBus.Subscribe(TokenCreated, rm.HandleEvent); err != nil {
		return fmt.Errorf("订阅TokenCreated事件失败: %v", err)
	}
	if err := rm.eventBus.Subscribe(TokenDeleted, rm.HandleEvent); err != nil {
		return fmt.Errorf("订阅TokenDeleted事件失败: %v", err)
	}
	if err := rm.eventBus.Subscribe(BindingRequested, rm.HandleEvent); err != nil {
		return fmt.Errorf("订阅BindingRequested事件失败: %v", err)
	}
	if err := rm.eventBus.Subscribe(MixedModeChanged, rm.HandleEvent); err != nil {
		return fmt.Errorf("订阅MixedModeChanged事件失败: %v", err)
	}

	log.Printf("ReceiverManager启动成功")
	return nil
}

// Stop 停止Receiver管理器
func (rm *ReceiverManager) Stop() error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if !rm.running {
		return nil
	}

	rm.running = false
	if rm.cancel != nil {
		rm.cancel()
	}

	log.Printf("ReceiverManager停止成功")
	return nil
}

// HandleEvent 处理事件
func (rm *ReceiverManager) HandleEvent(event *Event) error {
	switch event.Type {
	case TokenCreated:
		return rm.handleTokenCreated(event)
	case TokenDeleted:
		return rm.handleTokenDeleted(event)
	case BindingRequested:
		return rm.handleBindingRequested(event)
	case MixedModeChanged:
		return rm.handleMixedModeChanged(event)
	default:
		return nil
	}
}

// AddNewReceiverWithCapabilities 创建支持自定义ID和能力的Receiver
func (rm *ReceiverManager) AddNewReceiverWithCapabilities(id string, capabilities []string) *Receiver {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 如果没有提供能力，默认为 stock_a
	if len(capabilities) == 0 {
		capabilities = []string{"stock_a"}
	}

	// 应用特殊规则
	capabilities = applySpecialRulesToCapabilities(capabilities)

	receiver := &Receiver{
		ID:             id,
		TokenID:        "", // 初始时未绑定
		Used:           0,
		Capability:     capabilities,
		BindProducts:   make([]*Product, 0),
		capabilityBits: calculateCapabilityBitsFromStrings(capabilities),
	}

	rm.receivers[id] = receiver
	log.Printf("创建 Receiver: %s (产品支持类型: %v)", id, capabilities)

	// 发布Receiver创建事件
	receiverData := &ReceiverData{
		Receiver:        receiver,
		EnableMixedMode: rm.enableMixedMode,
	}
	event := NewEvent(ReceiverCreated, receiverData, rm.GetName())
	if err := rm.eventBus.Publish(event); err != nil {
		log.Printf("发布ReceiverCreated事件失败: %v", err)
	}

	// 尝试绑定到可用的Token
	rm.tryBindReceiver(id)

	return receiver
}

// RemoveReceiver 删除Receiver
func (rm *ReceiverManager) RemoveReceiver(receiverID string) bool {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	receiver, exists := rm.receivers[receiverID]
	if !exists {
		return false
	}

	log.Printf("删除 Receiver: %s (能力: %v, 绑定Token: %s， 解绑产品: %d 个)",
		receiverID, receiver.Capability, receiver.TokenID, len(receiver.BindProducts))

	// 发布Receiver删除事件，通知其他管理器处理相关资源
	receiverData := &ReceiverData{
		Receiver: receiver,
	}
	event := NewEvent(ReceiverDeleted, receiverData, rm.GetName())
	if err := rm.eventBus.Publish(event); err != nil {
		log.Printf("发布ReceiverDeleted事件失败: %v", err)
	}

	// 如果绑定了Token，更新Token连接数
	if receiver.TokenID != "" && rm.tokenManager != nil {
		if err := rm.tokenManager.UpdateTokenConnNum(receiver.TokenID, -1); err != nil {
			log.Printf("更新Token连接数失败: %v", err)
		}
	}

	// 从可用切片中移除
	rm.removeFromAvailableReceivers(receiver)
	// 从本地存储中删除
	delete(rm.unbindedReceivers, receiverID)
	delete(rm.receivers, receiverID)

	return true
}

// GetReceiver 获取Receiver
func (rm *ReceiverManager) GetReceiver(receiverID string) (*Receiver, bool) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()
	receiver, exists := rm.receivers[receiverID]
	return receiver, exists
}

// GetAllReceivers 获取所有Receiver
func (rm *ReceiverManager) GetAllReceivers() map[string]*Receiver {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	result := make(map[string]*Receiver)
	for k, v := range rm.receivers {
		result[k] = v
	}
	return result
}

// GetAvailableReceiversByType 获取按类型分组的可用Receiver
func (rm *ReceiverManager) GetAvailableReceiversByType() map[int][]*Receiver {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	result := make(map[int][]*Receiver)
	for k, v := range rm.availableReceiversByType {
		result[k] = make([]*Receiver, len(v))
		copy(result[k], v)
	}
	return result
}

// UpdateReceiverUsage 更新Receiver使用量
func (rm *ReceiverManager) UpdateReceiverUsage(receiverID string, usedDelta int) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	receiver, exists := rm.receivers[receiverID]
	if !exists {
		return fmt.Errorf("Receiver不存在: %s", receiverID)
	}

	oldUsed := receiver.Used
	receiver.Used += usedDelta
	if receiver.Used < 0 {
		receiver.Used = 0
	}

	// 如果使用量变化影响可用性，更新可用切片
	if oldUsed < 500 && receiver.Used >= 500 {
		// 从可用变为满载
		rm.removeFromAvailableReceivers(receiver)
		log.Printf("Receiver %s 已满载，从可用列表中移除", receiverID)
	} else if oldUsed >= 500 && receiver.Used < 500 && receiver.TokenID != "" {
		// 从满载变为可用
		rm.addToAvailableReceivers(receiver)
		log.Printf("Receiver %s 重新可用，加入可用列表", receiverID)
	}

	return nil
}

// SetMixedMode 设置混用模式
func (rm *ReceiverManager) SetMixedMode(enabled bool) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if rm.enableMixedMode != enabled {
		rm.enableMixedMode = enabled
		log.Printf("ReceiverManager混用模式设置为: %v", enabled)

		// 发布混用模式变更事件
		data := map[string]interface{}{
			"enable_mixed_mode": enabled,
		}
		event := NewEvent(MixedModeChanged, data, rm.GetName())
		if err := rm.eventBus.Publish(event); err != nil {
			log.Printf("发布MixedModeChanged事件失败: %v", err)
		}
	}
}

// addToAvailableReceivers 将receiver添加到对应类型的可用切片中
func (rm *ReceiverManager) addToAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	rm.availableReceiversByType[capType] = append(rm.availableReceiversByType[capType], receiver)
}

// removeFromAvailableReceivers 从对应类型的可用切片中移除receiver
func (rm *ReceiverManager) removeFromAvailableReceivers(receiver *Receiver) {
	capType := receiver.capabilityBits
	receivers := rm.availableReceiversByType[capType]
	for i, r := range receivers {
		if r.ID == receiver.ID {
			// 移除该元素
			rm.availableReceiversByType[capType] = append(receivers[:i], receivers[i+1:]...)
			break
		}
	}
}

// tryBindReceiver 尝试绑定Receiver到可用的Token
func (rm *ReceiverManager) tryBindReceiver(receiverID string) {
	receiver := rm.receivers[receiverID]

	// 将Receiver放入未绑定列表，等待Token管理器处理
	rm.unbindedReceivers[receiverID] = receiver
	log.Printf("Receiver %s 等待绑定 (能力: %v)", receiverID, receiver.Capability)

	// 发布绑定请求事件
	bindingData := map[string]interface{}{
		"type":        "receiver_binding",
		"receiver_id": receiverID,
		"receiver":    receiver,
	}
	event := NewEvent(BindingRequested, bindingData, rm.GetName())
	if err := rm.eventBus.Publish(event); err != nil {
		log.Printf("发布BindingRequested事件失败: %v", err)
	}
}

// handleTokenCreated 处理Token创建事件
func (rm *ReceiverManager) handleTokenCreated(event *Event) error {
	log.Printf("ReceiverManager收到TokenCreated事件，尝试绑定未绑定的Receiver")

	rm.mu.RLock()
	unbindedReceivers := make([]string, 0, len(rm.unbindedReceivers))
	for receiverID := range rm.unbindedReceivers {
		unbindedReceivers = append(unbindedReceivers, receiverID)
	}
	rm.mu.RUnlock()

	// 为每个未绑定的Receiver发布绑定请求
	for _, receiverID := range unbindedReceivers {
		rm.mu.RLock()
		receiver, exists := rm.receivers[receiverID]
		rm.mu.RUnlock()

		if !exists {
			continue
		}

		bindingData := map[string]interface{}{
			"type":        "receiver_binding",
			"receiver_id": receiverID,
			"receiver":    receiver,
		}
		bindEvent := NewEvent(BindingRequested, bindingData, rm.GetName())
		if err := rm.eventBus.Publish(bindEvent); err != nil {
			log.Printf("发布Receiver绑定请求失败: %v", err)
		}
	}

	return nil
}

// handleTokenDeleted 处理Token删除事件
func (rm *ReceiverManager) handleTokenDeleted(event *Event) error {
	tokenData, ok := event.Data.(*TokenData)
	if !ok {
		return fmt.Errorf("TokenDeleted事件数据格式错误")
	}

	token := tokenData.Token
	log.Printf("ReceiverManager收到TokenDeleted事件: %s", token.ID)

	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 解绑所有相关的Receiver
	var unboundReceivers []string
	for _, receiver := range rm.receivers {
		if receiver.TokenID == token.ID {
			receiver.TokenID = ""
			unboundReceivers = append(unboundReceivers, receiver.ID)
			// 从可用切片中移除
			rm.removeFromAvailableReceivers(receiver)
			// 放入未绑定列表
			rm.unbindedReceivers[receiver.ID] = receiver
		}
	}

	if len(unboundReceivers) > 0 {
		log.Printf("Token %s 删除后，解绑了 %d 个 Receiver: %v", token.ID, len(unboundReceivers), unboundReceivers)

		// 尝试重新绑定到其他Token
		for _, receiverID := range unboundReceivers {
			rm.tryBindReceiver(receiverID)
		}
	}

	return nil
}

// handleBindingRequested 处理绑定请求事件
func (rm *ReceiverManager) handleBindingRequested(event *Event) error {
	data, ok := event.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("绑定请求事件数据格式错误")
	}

	bindingType, ok := data["type"].(string)
	if !ok {
		return fmt.Errorf("绑定请求类型缺失")
	}

	if bindingType == "token_binding" {
		return rm.handleTokenBindingRequest(data)
	} else if bindingType == "receiver_binding" {
		return rm.handleReceiverBindingRequest(data)
	}

	return nil
}

// handleTokenBindingRequest 处理Token绑定请求
func (rm *ReceiverManager) handleTokenBindingRequest(data map[string]interface{}) error {
	tokenID, ok := data["token_id"].(string)
	if !ok {
		return fmt.Errorf("Token ID缺失")
	}

	token, ok := data["token"].(*Token)
	if !ok {
		return fmt.Errorf("Token数据缺失")
	}

	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 查找匹配的未绑定receiver并绑定
	for receiverID, receiver := range rm.unbindedReceivers {
		if isCapabilityCompatible(token.capabilityBits, receiver.capabilityBits, rm.enableMixedMode) {
			// 检查Token连接数限制
			if rm.tokenManager != nil {
				if tokenInfo, exists := rm.tokenManager.GetToken(tokenID); exists && tokenInfo.ConnNum >= 10 {
					continue // Token连接数已满
				}
			}

			// 绑定receiver到token
			receiver.TokenID = tokenID

			// 更新Token连接数
			if rm.tokenManager != nil {
				if err := rm.tokenManager.UpdateTokenConnNum(tokenID, 1); err != nil {
					log.Printf("更新Token连接数失败: %v", err)
				}
				// 标记Token已绑定
				rm.tokenManager.MarkTokenBound(tokenID)
			}

			// 将receiver加入到对应类型的可用切片中（如果未满）
			if receiver.Used < 500 {
				rm.addToAvailableReceivers(receiver)
			}

			// 从未绑定列表中移除
			delete(rm.unbindedReceivers, receiverID)

			log.Printf("Token %s 自动绑定到 Receiver %s (Token能力: %v, Receiver能力: %v)",
				tokenID, receiverID, token.Capability, receiver.Capability)

			// 发布绑定完成事件
			bindingData := map[string]interface{}{
				"type":        "binding_completed",
				"token_id":    tokenID,
				"receiver_id": receiverID,
			}
			event := NewEvent(BindingCompleted, bindingData, rm.GetName())
			if err := rm.eventBus.Publish(event); err != nil {
				log.Printf("发布BindingCompleted事件失败: %v", err)
			}

			return nil // 只绑定一个receiver
		}
	}

	return nil
}

// handleReceiverBindingRequest 处理Receiver绑定请求
func (rm *ReceiverManager) handleReceiverBindingRequest(data map[string]interface{}) error {
	// 这里主要是处理来自其他管理器的Receiver绑定请求
	// 当前实现中主要由Token绑定驱动，所以这里可以简单处理
	return nil
}

// handleMixedModeChanged 处理混用模式变更事件
func (rm *ReceiverManager) handleMixedModeChanged(event *Event) error {
	data, ok := event.Data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("MixedModeChanged事件数据格式错误")
	}

	enableMixed, ok := data["enable_mixed_mode"].(bool)
	if !ok {
		return fmt.Errorf("混用模式设置缺失")
	}

	rm.mu.Lock()
	rm.enableMixedMode = enableMixed
	rm.mu.Unlock()

	log.Printf("ReceiverManager收到混用模式变更事件: %v", enableMixed)
	return nil
}

// MarkReceiverBound 标记Receiver已绑定（从未绑定列表中移除）
func (rm *ReceiverManager) MarkReceiverBound(receiverID string) {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	delete(rm.unbindedReceivers, receiverID)
	log.Printf("Receiver %s 已绑定，从未绑定列表中移除", receiverID)
}
