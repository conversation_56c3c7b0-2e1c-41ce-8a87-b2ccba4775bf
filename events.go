package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// EventType 定义事件类型
type EventType string

// 事件类型常量定义
const (
	// Token相关事件
	TokenCreated  EventType = "token_created"
	TokenDeleted  EventType = "token_deleted"
	TokenUpdated  EventType = "token_updated"
	TokenBound    EventType = "token_bound"
	TokenUnbound  EventType = "token_unbound"

	// Receiver相关事件
	ReceiverCreated   EventType = "receiver_created"
	ReceiverDeleted   EventType = "receiver_deleted"
	ReceiverUpdated   EventType = "receiver_updated"
	ReceiverBound     EventType = "receiver_bound"
	ReceiverUnbound   EventType = "receiver_unbound"
	ReceiverAvailable EventType = "receiver_available"
	ReceiverFull      EventType = "receiver_full"

	// Product相关事件
	ProductCreated   EventType = "product_created"
	ProductDeleted   EventType = "product_deleted"
	ProductAllocated EventType = "product_allocated"
	ProductBound     EventType = "product_bound"
	ProductUnbound   EventType = "product_unbound"

	// 绑定相关事件
	BindingRequested EventType = "binding_requested"
	BindingCompleted EventType = "binding_completed"
	BindingFailed    EventType = "binding_failed"

	// 系统事件
	SystemStarted EventType = "system_started"
	SystemStopped EventType = "system_stopped"
	MixedModeChanged EventType = "mixed_mode_changed"
)

// Event 事件结构体
type Event struct {
	Type      EventType   `json:"type"`
	Data      interface{} `json:"data"`
	Source    string      `json:"source"`
	Timestamp time.Time   `json:"timestamp"`
	ID        string      `json:"id"`
}

// NewEvent 创建新事件
func NewEvent(eventType EventType, data interface{}, source string) *Event {
	return &Event{
		Type:      eventType,
		Data:      data,
		Source:    source,
		Timestamp: time.Now(),
		ID:        fmt.Sprintf("%s_%d", source, time.Now().UnixNano()),
	}
}

// EventHandler 事件处理函数类型
type EventHandler func(event *Event) error

// EventBus 事件总线接口
type EventBus interface {
	// Subscribe 订阅事件类型
	Subscribe(eventType EventType, handler EventHandler) error
	// Unsubscribe 取消订阅
	Unsubscribe(eventType EventType, handler EventHandler) error
	// Publish 发布事件
	Publish(event *Event) error
	// Start 启动事件总线
	Start(ctx context.Context) error
	// Stop 停止事件总线
	Stop() error
	// IsRunning 检查是否运行中
	IsRunning() bool
}

// ResourceManager 资源管理器接口
type ResourceManager interface {
	// HandleEvent 处理事件
	HandleEvent(event *Event) error
	// Start 启动管理器
	Start(ctx context.Context) error
	// Stop 停止管理器
	Stop() error
	// GetName 获取管理器名称
	GetName() string
}

// MemoryEventBus 内存事件总线实现
type MemoryEventBus struct {
	subscribers map[EventType][]EventHandler
	eventChan   chan *Event
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	mu          sync.RWMutex
	running     bool
}

// NewMemoryEventBus 创建内存事件总线
func NewMemoryEventBus() *MemoryEventBus {
	return &MemoryEventBus{
		subscribers: make(map[EventType][]EventHandler),
		eventChan:   make(chan *Event, 1000), // 缓冲区大小1000
	}
}

// Subscribe 订阅事件
func (bus *MemoryEventBus) Subscribe(eventType EventType, handler EventHandler) error {
	bus.mu.Lock()
	defer bus.mu.Unlock()

	bus.subscribers[eventType] = append(bus.subscribers[eventType], handler)
	log.Printf("事件订阅: %s (处理器数量: %d)", eventType, len(bus.subscribers[eventType]))
	return nil
}

// Unsubscribe 取消订阅
func (bus *MemoryEventBus) Unsubscribe(eventType EventType, handler EventHandler) error {
	bus.mu.Lock()
	defer bus.mu.Unlock()

	handlers := bus.subscribers[eventType]
	for i, h := range handlers {
		// 比较函数指针（简化实现）
		if fmt.Sprintf("%p", h) == fmt.Sprintf("%p", handler) {
			bus.subscribers[eventType] = append(handlers[:i], handlers[i+1:]...)
			log.Printf("取消事件订阅: %s (剩余处理器数量: %d)", eventType, len(bus.subscribers[eventType]))
			return nil
		}
	}
	return fmt.Errorf("未找到要取消的事件处理器: %s", eventType)
}

// Publish 发布事件
func (bus *MemoryEventBus) Publish(event *Event) error {
	if !bus.IsRunning() {
		return fmt.Errorf("事件总线未运行")
	}

	select {
	case bus.eventChan <- event:
		return nil
	case <-bus.ctx.Done():
		return fmt.Errorf("事件总线已停止")
	default:
		return fmt.Errorf("事件队列已满，无法发布事件: %s", event.Type)
	}
}

// Start 启动事件总线
func (bus *MemoryEventBus) Start(ctx context.Context) error {
	bus.mu.Lock()
	defer bus.mu.Unlock()

	if bus.running {
		return fmt.Errorf("事件总线已在运行")
	}

	bus.ctx, bus.cancel = context.WithCancel(ctx)
	bus.running = true

	// 启动事件处理goroutine
	bus.wg.Add(1)
	go bus.processEvents()

	log.Printf("事件总线启动成功")
	return nil
}

// Stop 停止事件总线
func (bus *MemoryEventBus) Stop() error {
	bus.mu.Lock()
	if !bus.running {
		bus.mu.Unlock()
		return nil
	}
	bus.running = false
	bus.mu.Unlock()

	// 取消上下文
	if bus.cancel != nil {
		bus.cancel()
	}

	// 等待goroutine结束
	bus.wg.Wait()

	log.Printf("事件总线停止成功")
	return nil
}

// IsRunning 检查是否运行中
func (bus *MemoryEventBus) IsRunning() bool {
	bus.mu.RLock()
	defer bus.mu.RUnlock()
	return bus.running
}

// processEvents 处理事件的goroutine
func (bus *MemoryEventBus) processEvents() {
	defer bus.wg.Done()

	for {
		select {
		case event := <-bus.eventChan:
			bus.handleEvent(event)
		case <-bus.ctx.Done():
			log.Printf("事件处理goroutine退出")
			return
		}
	}
}

// handleEvent 处理单个事件
func (bus *MemoryEventBus) handleEvent(event *Event) {
	bus.mu.RLock()
	handlers := bus.subscribers[event.Type]
	bus.mu.RUnlock()

	if len(handlers) == 0 {
		log.Printf("事件无处理器: %s (来源: %s)", event.Type, event.Source)
		return
	}

	log.Printf("处理事件: %s (来源: %s, 处理器数量: %d)", event.Type, event.Source, len(handlers))

	// 并发处理所有订阅者
	var wg sync.WaitGroup
	for _, handler := range handlers {
		wg.Add(1)
		go func(h EventHandler) {
			defer wg.Done()
			if err := h(event); err != nil {
				log.Printf("事件处理失败: %s, 错误: %v", event.Type, err)
			}
		}(handler)
	}
	wg.Wait()
}
