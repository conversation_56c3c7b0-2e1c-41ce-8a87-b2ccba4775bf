package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	// 创建协调器实例
	coordinator := NewCoordinator()

	// 启动协调器和事件系统
	ctx := context.Background()
	if err := coordinator.Start(ctx); err != nil {
		log.Fatalf("启动协调器失败: %v", err)
	}

	// 设置优雅关闭
	defer func() {
		log.Println("正在关闭系统...")
		if err := coordinator.Stop(); err != nil {
			log.Printf("关闭协调器失败: %v", err)
		}
		log.Println("系统已关闭")
	}()

	// 初始化一些测试数据
	log.Println("初始化测试数据...")

	// 创建不同类型的 Token
	_ = coordinator.AddNewTokenWithCapabilities("company", "", "", "", 3000, []string{"stock_us", "stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("utgl", "", "", "", 5000, []string{"stock_us", "stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("erik", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("frewen", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("leo", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("greet", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("ryan", "", "", "", 500, []string{"stock_hk"})
	_ = coordinator.AddNewTokenWithCapabilities("alex", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("jade", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("meyer", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("cavill", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("lee", "", "", "", 500, []string{"stock_us"})
	_ = coordinator.AddNewTokenWithCapabilities("ariel", "", "", "", 500, []string{"stock_us"})

	// 创建组合类型的 Token
	// tokenCombo := coordinator.AddNewTokenWithCapabilities("token-5", "", "", "", 1200, []string{"stock_us", "stock_hk", "stock_a"})

	log.Printf("创建了 %d 个 Token", len(coordinator.GetTokens()))

	// 创建不同类型的 Receiver
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-1", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-2", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-3", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-4", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-5", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-6", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-7", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-8", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-9", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-10", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-11", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-12", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-13", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-14", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-15", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-a-16", []string{"stock_a"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-1", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-2", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-3", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-4", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-5", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-6", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-us-7", []string{"stock_us"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-1", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-2", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-3", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-4", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-5", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-6", []string{"stock_hk"})
	_ = coordinator.AddNewReceiverWithCapabilities("receiver-hk-7", []string{"stock_hk"})

	log.Printf("创建了 %d 个 Receiver", len(coordinator.GetReceivers()))

	/* 	// 打印初始化信息
	   	log.Printf("Token 信息:")
	   	log.Printf("  %s: 能力=%v, 限额=%d", token1.ID, token1.Capability, token1.Limit)
	   	log.Printf("  %s: 能力=%v, 限额=%d", token2.ID, token2.Capability, token2.Limit)
	   	log.Printf("  %s: 能力=%v, 限额=%d", token3.ID, token3.Capability, token3.Limit)
	   	log.Printf("  %s: 能力=%v, 限额=%d", token4.ID, token4.Capability, token4.Limit)
	   	log.Printf("  %s: 能力=%v, 限额=%d", tokenCombo.ID, tokenCombo.Capability, tokenCombo.Limit)

	   	log.Printf("Receiver 信息:")
	   	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver1.ID, receiver1.Capability, receiver1.TokenID)
	   	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver2.ID, receiver2.Capability, receiver2.TokenID)
	   	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver3.ID, receiver3.Capability, receiver3.TokenID)
	   	log.Printf("  %s: 能力=%v, 绑定Token=%s", receiver4.ID, receiver4.Capability, receiver4.TokenID) */

	// 等待事件处理完成
	if err := coordinator.WaitForEventProcessing(100); err != nil {
		log.Printf("等待事件处理失败: %v", err)
	}

	// 启动 API 服务器
	log.Println("启动 API 服务器在端口 8080...")
	log.Println("API 文档:")
	log.Println("  Token 管理:")
	log.Println("    POST   /api/v1/tokens          - 创建 Token")
	log.Println("    GET    /api/v1/tokens          - 获取所有 Token")
	log.Println("    GET    /api/v1/tokens/:id      - 获取指定 Token")
	log.Println("    PUT    /api/v1/tokens/:id      - 更新 Token")
	log.Println("    DELETE /api/v1/tokens/:id      - 删除 Token")
	log.Println("  Receiver 管理:")
	log.Println("    POST   /api/v1/receivers       - 创建 Receiver")
	log.Println("    GET    /api/v1/receivers       - 获取所有 Receiver")
	log.Println("    GET    /api/v1/receivers/:id   - 获取指定 Receiver")
	log.Println("    PUT    /api/v1/receivers/:id   - 更新 Receiver")
	log.Println("    DELETE /api/v1/receivers/:id   - 删除 Receiver")
	log.Println("  Product 管理:")
	log.Println("    POST   /api/v1/products          - 创建产品")
	log.Println("    GET    /api/v1/products          - 获取所有产品")
	log.Println("    GET    /api/v1/products/:code    - 获取指定产品")
	log.Println("    PUT    /api/v1/products/:code    - 更新产品")
	log.Println("    DELETE /api/v1/products/:code    - 删除产品")
	log.Println("    POST   /api/v1/products/allocate - 分配产品")
	log.Println("    POST   /api/v1/products/batch    - 批量创建产品")
	log.Println("    DELETE /api/v1/products/batch    - 批量删除产品")
	log.Println("    GET    /api/v1/products/stats    - 获取统计信息")
	log.Println("  系统配置:")
	log.Println("    GET    /api/v1/system/status           - 获取系统状态")
	log.Println("    GET    /api/v1/system/health           - 获取架构健康状态")
	log.Println("    PUT    /api/v1/system/mixed-mode       - 设置混用模式")

	// 设置信号处理，支持优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动API服务器
	go func() {
		StartAPIServer(coordinator, "8080")
	}()

	// 等待关闭信号
	<-sigChan
	log.Println("收到关闭信号，正在优雅关闭...")
}
